interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": false, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '129'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: "{\n  \"id\": \"chatcmpl-B7RVMAEu5kjdxdrdK3Jceoc7HUfuc\",\n  \"object\":
        \"chat.completion\",\n  \"created\": 1741113724,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
        \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
        \"assistant\",\n        \"content\": \"foo\",\n        \"refusal\": null\n
        \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
        \ ],\n  \"usage\": {\n    \"prompt_tokens\": 11,\n    \"completion_tokens\":
        2,\n    \"total_tokens\": 13,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
        0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
        {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
        0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
        \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b382e818d66991-PHL
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Tue, 04 Mar 2025 18:42:04 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=BvxgIPA8Op6cZugXWRqdnBYaO0KyW4nRMPQVzle.TYw-1741113724-*******-EHaIc6q.zV2RRwITJGHyoztOyqq0qEF2WvdugcM8V.lQ4rFsFcf2_4r2K3WCCjMj.Rt9UUTLbWNIxHKQH.U78yfkftRnIpLIcq3n2b0H9YU;
        path=/; expires=Tue, 04-Mar-25 19:12:04 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=_DEpXnqnKgdSj.B8DBjyOpUjSYKwHoV8FexKo1IYRrU-1741113724685-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      content-length:
      - '767'
      openai-organization:
      - langchain
      openai-processing-ms:
      - '304'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_cdfbcffffbd0998fa53197d4ae3fb5e4
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": false, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '129'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: "{\n  \"id\": \"chatcmpl-B7RVN969rIHEL2WyXNwr9EZnpssxh\",\n  \"object\":
        \"chat.completion\",\n  \"created\": 1741113725,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
        \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
        \"assistant\",\n        \"content\": \"foo\",\n        \"refusal\": null\n
        \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
        \ ],\n  \"usage\": {\n    \"prompt_tokens\": 11,\n    \"completion_tokens\":
        2,\n    \"total_tokens\": 13,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
        0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
        {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
        0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
        \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-RAY:
      - 91b382ef498f41bb-EWR
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Tue, 04 Mar 2025 18:42:05 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=NPtc2hkfnprR2kEYrQqB3fLn0v81YGAcvzoOL.PtIZk-1741113725-*******-v43_9eydHpOh21WdUb6X18CXkqUOM6y7FJd0xCoERzfGyf93kSspKzGcA12uNe3DIe_o_eUBx2OqRJ.KeDhQ4iNRWU28nRXmkS5mkCegXfY;
        path=/; expires=Tue, 04-Mar-25 19:12:05 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=9zdDpzs1XYkpJZDzYI8r_5eoUzo96LipxUwiL7kvQ2s-1741113725729-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      content-length:
      - '767'
      openai-organization:
      - langchain
      openai-processing-ms:
      - '290'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_3f36155c682a8655412c2a22fedc3224
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": true, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '128'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: 'data: {"id":"chatcmpl-B7RVO4vQHCye4bIZiR0K1ZHNAmbbc","object":"chat.completion.chunk","created":1741113726,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVO4vQHCye4bIZiR0K1ZHNAmbbc","object":"chat.completion.chunk","created":1741113726,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"foo"},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVO4vQHCye4bIZiR0K1ZHNAmbbc","object":"chat.completion.chunk","created":1741113726,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}


        data: [DONE]


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b382f538a54267-EWR
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Tue, 04 Mar 2025 18:42:06 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=FC05nbvg61n2qMt8sgsm3LDjZDpmuI7vr_LoVeUbk8I-1741113726-*******-Ivf.eDfJBykztzEZ.7qGh7M7UFRfmxGwI5ERKOqde09eW43QnE_ANRDS8Gyu7MInGrOIh7X6qQQ3EZ1vRufgmSjbOZg6g7NsbcYNhYsuHaA;
        path=/; expires=Tue, 04-Mar-25 19:12:06 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=zPbvtBrVA.R.CY_i2WlbtWkvDJImefWhiJnqTtsvxY0-1741113726648-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - langchain
      openai-processing-ms:
      - '240'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_1881bad1a79afd8f02ca10be54086965
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": true, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '128'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: 'data: {"id":"chatcmpl-B7RVPJ0r9cUufaFQM7KstOwBTzRcU","object":"chat.completion.chunk","created":1741113727,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVPJ0r9cUufaFQM7KstOwBTzRcU","object":"chat.completion.chunk","created":1741113727,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"foo"},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVPJ0r9cUufaFQM7KstOwBTzRcU","object":"chat.completion.chunk","created":1741113727,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}


        data: [DONE]


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b382facff332cc-PHL
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Tue, 04 Mar 2025 18:42:07 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=cGeM6O.aCDg8O9qzPWNKYVygcZYrsWDkpFdrsPgmnC0-1741113727-*******-NrwbUlWOM9kYd6yNf.X0QkzVpxbsd_4MBFvwQhdaYgQhZxIiycAxs1CQuQWWBTDfR6xJvT7TEyh9I7C2JNRp0q2QKWB8DSz_IplMlS4NI0o;
        path=/; expires=Tue, 04-Mar-25 19:12:07 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=VwBjUQCTigzITZGDzwoeL7_vWaVtlJip1ET8kUSpmEs-1741113727547-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - langchain
      openai-processing-ms:
      - '243'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_6b6a7ba33989f471ebb2782c8a1adcf5
    status:
      code: 200
      message: OK
version: 1
