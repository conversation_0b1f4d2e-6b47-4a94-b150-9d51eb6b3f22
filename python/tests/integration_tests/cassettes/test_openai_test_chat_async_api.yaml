interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": false, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '129'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: "{\n  \"id\": \"chatcmpl-B7RVQqbtEfYOLFDLksf7zIzWH31zC\",\n  \"object\":
        \"chat.completion\",\n  \"created\": 1741113728,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
        \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
        \"assistant\",\n        \"content\": \"foo\",\n        \"refusal\": null\n
        \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
        \ ],\n  \"usage\": {\n    \"prompt_tokens\": 11,\n    \"completion_tokens\":
        2,\n    \"total_tokens\": 13,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
        0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
        {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
        0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
        \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b38301bd355e73-EWR
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Tue, 04 Mar 2025 18:42:08 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=FtKEmTrawH1IgTe39ooax1mrywaX9otz.V1LZrtp5mk-1741113728-*******-TFYxCRw63w6b.YGwdP6pGg9x7ApSV8DNG3apiHTNO9psBsB5rs2XVDslgh5ecI6XKeBmGZTKakWpdycuMpPHY.b7l6qhY882Vkk46BVy_sg;
        path=/; expires=Tue, 04-Mar-25 19:12:08 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=T9G4GKEYBeqqX92g.OyvE1bos0OiMMRzIc1mLmCgFa0-1741113728721-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      content-length:
      - '767'
      openai-organization:
      - langchain
      openai-processing-ms:
      - '307'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_f3fa984b372c93a635b4e1096b8facd4
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": false, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '129'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: "{\n  \"id\": \"chatcmpl-B7RVRm23e9KvgJ1uIhfXbgl35tGhW\",\n  \"object\":
        \"chat.completion\",\n  \"created\": 1741113729,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
        \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
        \"assistant\",\n        \"content\": \"foo\",\n        \"refusal\": null\n
        \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
        \ ],\n  \"usage\": {\n    \"prompt_tokens\": 11,\n    \"completion_tokens\":
        2,\n    \"total_tokens\": 13,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
        0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
        {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
        0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
        \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-RAY:
      - 91b383078e6d41b5-EWR
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Tue, 04 Mar 2025 18:42:09 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=Nr89IP9RudG_4cbkH63YFWWWUskzXZVmzD6.B8arVmQ-1741113729-*******-MJPZOZfBGxi.rrtQGYSvh_aqhTPN2AFJelrUGmAy7suZzVIfcGVhp2enVWvfEQAX9wH4B1XqeDmCdW7A6TsZmHwhbZM4W6md2UStMUWMUnM;
        path=/; expires=Tue, 04-Mar-25 19:12:09 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=CnOioPD8TS38ueU_XE4wiZvkK8zXB60iNWM33B4Bffo-1741113729604-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      content-length:
      - '767'
      openai-organization:
      - langchain
      openai-processing-ms:
      - '266'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_f7d7f85afb1e1f7a48f636b3164f1653
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": true, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '128'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: 'data: {"id":"chatcmpl-B7RVSjsrkEQe7snQYykzS5TOAKzkN","object":"chat.completion.chunk","created":1741113730,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVSjsrkEQe7snQYykzS5TOAKzkN","object":"chat.completion.chunk","created":1741113730,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"foo"},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVSjsrkEQe7snQYykzS5TOAKzkN","object":"chat.completion.chunk","created":1741113730,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}


        data: [DONE]


        '
    headers:
      CF-RAY:
      - 91b3830e69a4421c-EWR
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Tue, 04 Mar 2025 18:42:10 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=EOguoga7uK_dQQNLlpZx1wi5xtjHQTY67v4qQbknn4w-1741113730-*******-JtZ1PpePOPSJA9TpuPlgzHF55pC69G3FA6MBVMdFIfjTppP1vAAh5ynv33JSZYBRG.etwy_EKz4iYoyQQYB4G0Aw34aDHpdlqI1HjbXOpVw;
        path=/; expires=Tue, 04-Mar-25 19:12:10 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=ir4OcP8nE8lJd21HgjM9yQdWKwguRJd2_yTJX5q3WBI-1741113730851-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - langchain
      openai-processing-ms:
      - '200'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_faa4b21c969dbf519eb767adbe542a50
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo''"}], "model": "gpt-3.5-turbo",
      "seed": 42, "stream": true, "temperature": 0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '128'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: 'data: {"id":"chatcmpl-B7RVTX85izAg8Blrepr4yWFhxsLfi","object":"chat.completion.chunk","created":1741113731,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVTX85izAg8Blrepr4yWFhxsLfi","object":"chat.completion.chunk","created":1741113731,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"foo"},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-B7RVTX85izAg8Blrepr4yWFhxsLfi","object":"chat.completion.chunk","created":1741113731,"model":"gpt-3.5-turbo-0125","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}


        data: [DONE]


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b38314cd2042eb-EWR
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Tue, 04 Mar 2025 18:42:11 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=Ao0rQhCnYKU9Oe7rtglyRfBGWb8HDSrh0FL2IsXKtUM-1741113731-*******-uhqZZjUkQzaSkQ96bosaTamOJ7pRkWYn.z.2jeptucyHe2hNfQk.Nge42evg_it2Qh3bRLLFKoECSGOnOG51uJEEEtKoAoUHMBRcp03Jdbo;
        path=/; expires=Tue, 04-Mar-25 19:12:11 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=B_OLTX_QCwSDDyN2Kv9jp4OnKp2AzD1JvMgVQDPOJoA-1741113731665-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - langchain
      openai-processing-ms:
      - '216'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999980'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_4021a63b617b8ee1f23cc9dd055cd456
    status:
      code: 200
      message: OK
version: 1
