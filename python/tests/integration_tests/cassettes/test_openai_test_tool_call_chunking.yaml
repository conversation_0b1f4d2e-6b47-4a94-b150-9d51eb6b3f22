interactions:
- request:
    body: '{"messages":[{"role":"user","content":"What''s the weather like in Paris?"}],"model":"gpt-4o","stream":true,"tool_choice":"auto","tools":[{"type":"function","function":{"name":"get_weather","description":"Get
      the current weather in a given city.","parameters":{"type":"object","properties":{"location":{"type":"string","description":"The
      city to get the weather for"}},"required":["location"]}}}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '396'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.68.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: 'data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{"role":"assistant","content":null,"tool_calls":[{"index":0,"id":"call_3nuop1P58UGYNd2zVPxDFExo","type":"function","function":{"name":"get_weather","arguments":""}}],"refusal":null},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"{\""}}]},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"location"}}]},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"\":\""}}]},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"Paris"}}]},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{"tool_calls":[{"index":0,"function":{"arguments":"\"}"}}]},"logprobs":null,"finish_reason":null}]}


        data: {"id":"chatcmpl-BOpXo2FaryhMGZSTINyPFRvGd2fBo","object":"chat.completion.chunk","created":1745257708,"model":"gpt-4o-2024-08-06","service_tier":"default","system_fingerprint":"fp_55d88aaf2f","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"tool_calls"}]}


        data: [DONE]


        '
    headers:
      CF-RAY:
      - 933eb66358bd78ea-LAX
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Mon, 21 Apr 2025 17:48:29 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=ZCY7YiZ0KYDP5iFOV.9jkiL31TAAWfev9BTWGRFVRoQ-1745257709-1.0.1.1-wUHqsX.wda1MMe8N0ghjtMYLPkdH1GqSl0NGU7nu22MjkYGiHYch9NGA1Jss0fudrSgzPZ1iyx47cB3QroqLPnyoiJ1fjt.Cj7dIYZibkLE;
        path=/; expires=Mon, 21-Apr-25 18:18:29 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=.TIzlaZK7SqT_rJQvQ11O24IRVWGCr2GBjKSyhl8q0k-1745257709005-0.0.1.1-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - langchain
      openai-processing-ms:
      - '613'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999989'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_1720ba3c7ba7c4cfc3e2f8fb4fe58eaa
    status:
      code: 200
      message: OK
version: 1
