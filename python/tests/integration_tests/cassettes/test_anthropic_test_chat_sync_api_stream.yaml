interactions:
- request:
    body: '{"max_tokens": 3, "messages": [{"role": "user", "content": "Say ''foo''"}],
      "model": "claude-3-5-haiku-latest", "temperature": 0, "stream": true}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '143'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.47.1
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
      x-stainless-stream-helper:
      - messages
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01Hwzd4txAVmazVvLdovsvkS","type":"message","role":"assistant","model":"claude-3-5-haiku-20241022","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":12,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":1}}  }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}             }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"foo"}             }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0         }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"max_tokens","stop_sequence":null},"usage":{"output_tokens":3}    }


        event: message_stop

        data: {"type":"message_stop"          }


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91c5fea44b97f94f-SJC
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Fri, 07 Mar 2025 00:32:16 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 39635d3b-ebfd-4987-9921-b228d48174d6
      anthropic-ratelimit-input-tokens-limit:
      - '400000'
      anthropic-ratelimit-input-tokens-remaining:
      - '400000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-07T00:32:16Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '80000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-07T00:32:16Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-07T00:32:16Z'
      anthropic-ratelimit-tokens-limit:
      - '480000'
      anthropic-ratelimit-tokens-remaining:
      - '480000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-07T00:32:16Z'
      request-id:
      - req_01FcgPFGJ6Tb8tT3EUSLPpgY
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 3, "messages": [{"role": "user", "content": "Say ''foo''"}],
      "model": "claude-3-5-haiku-latest", "temperature": 0, "stream": true}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '143'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.47.1
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
      x-stainless-stream-helper:
      - messages
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01WSTJKac5Wb7iJENXUySCxW","type":"message","role":"assistant","model":"claude-3-5-haiku-20241022","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":12,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":1}}      }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}             }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"foo"}
        }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0            }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"max_tokens","stop_sequence":null},"usage":{"output_tokens":3}      }


        event: message_stop

        data: {"type":"message_stop"    }


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91c5fea6f8c0fa66-SJC
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Fri, 07 Mar 2025 00:32:16 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 39635d3b-ebfd-4987-9921-b228d48174d6
      anthropic-ratelimit-input-tokens-limit:
      - '400000'
      anthropic-ratelimit-input-tokens-remaining:
      - '400000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-07T00:32:16Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '80000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-07T00:32:16Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-07T00:32:16Z'
      anthropic-ratelimit-tokens-limit:
      - '480000'
      anthropic-ratelimit-tokens-remaining:
      - '480000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-07T00:32:16Z'
      request-id:
      - req_01MKKZQwSMKg7Q58B2zpoFV1
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 3, "messages": [{"role": "user", "content": "Say ''foo''"}],
      "model": "claude-3-5-haiku-latest", "temperature": 0, "stream": true}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '143'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.47.1
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
      x-stainless-stream-helper:
      - messages
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01AeaCn9uGRwxr3xiTgfNYva","type":"message","role":"assistant","model":"claude-3-5-haiku-20241022","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":12,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":1}}             }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}      }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"foo"}      }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0     }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"max_tokens","stop_sequence":null},"usage":{"output_tokens":3}        }


        event: message_stop

        data: {"type":"message_stop"          }


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91c5feadee81f94f-SJC
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Fri, 07 Mar 2025 00:32:17 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 39635d3b-ebfd-4987-9921-b228d48174d6
      anthropic-ratelimit-input-tokens-limit:
      - '400000'
      anthropic-ratelimit-input-tokens-remaining:
      - '400000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-07T00:32:17Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '79000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-07T00:32:18Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-07T00:32:17Z'
      anthropic-ratelimit-tokens-limit:
      - '480000'
      anthropic-ratelimit-tokens-remaining:
      - '479000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-07T00:32:17Z'
      request-id:
      - req_01Li5TK9Jgy3ZHvWyc5Cz5df
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 3, "messages": [{"role": "user", "content": "Say ''foo''"}],
      "model": "claude-3-5-haiku-latest", "temperature": 0, "stream": true}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '143'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.47.1
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
      x-stainless-stream-helper:
      - messages
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01Esk9A39zpeoBVY1JDCi97h","type":"message","role":"assistant","model":"claude-3-5-haiku-20241022","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":12,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":1}}      }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}        }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"foo"}             }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0 }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"max_tokens","stop_sequence":null},"usage":{"output_tokens":3}  }


        event: message_stop

        data: {"type":"message_stop"           }


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91c5feb03a22fa66-SJC
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Fri, 07 Mar 2025 00:32:18 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 39635d3b-ebfd-4987-9921-b228d48174d6
      anthropic-ratelimit-input-tokens-limit:
      - '400000'
      anthropic-ratelimit-input-tokens-remaining:
      - '400000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-07T00:32:18Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '80000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-07T00:32:18Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-07T00:32:18Z'
      anthropic-ratelimit-tokens-limit:
      - '480000'
      anthropic-ratelimit-tokens-remaining:
      - '480000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-07T00:32:18Z'
      request-id:
      - req_01A4o7erXr72K1sT7rHcPXg8
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
version: 1
