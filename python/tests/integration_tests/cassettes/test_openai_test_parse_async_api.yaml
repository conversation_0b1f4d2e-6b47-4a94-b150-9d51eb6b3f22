interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo'' then stop."}], "model":
      "gpt-3.5-turbo", "max_tokens": 3, "seed": 42, "stream": false, "temperature":
      0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '157'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-helper-method:
      - beta.chat.completions.parse
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: "{\n  \"id\": \"chatcmpl-B7RWMOAZFipHRNoeFR00A2GoNQ46t\",\n  \"object\":
        \"chat.completion\",\n  \"created\": 1741113786,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
        \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
        \"assistant\",\n        \"content\": \"Foo\",\n        \"refusal\": null\n
        \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
        \ ],\n  \"usage\": {\n    \"prompt_tokens\": 14,\n    \"completion_tokens\":
        2,\n    \"total_tokens\": 16,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
        0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
        {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
        0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
        \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b3846d18da425d-EWR
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Tue, 04 Mar 2025 18:43:06 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=LIszzpRgrP_OW_VPQxAKvAxXs3W8k3uT78J04asKvYI-1741113786-1.0.1.1-9rYlwEEokCXCHW.zEBvOA1lcP8cke2cjS.mje2A9ZWMGOw4elIXR5KxMSafGpm1YRLmP9Ft8kbvD8qm5odVGTsvY4nvd.goXpAEPbr2rdjg;
        path=/; expires=Tue, 04-Mar-25 19:13:06 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=Vg1E1hcWetTFZFdAfEgBClakbpZlUjPu3vfCO6.VwCk-1741113786935-0.0.1.1-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      content-length:
      - '767'
      openai-organization:
      - langchain
      openai-processing-ms:
      - '372'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999991'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_3fbf7d9aebd6fc17b07d9032c2f4f182
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "Say ''foo'' then stop."}], "model":
      "gpt-3.5-turbo", "max_tokens": 3, "seed": 42, "stream": false, "temperature":
      0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '157'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-helper-method:
      - beta.chat.completions.parse
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: "{\n  \"id\": \"chatcmpl-B7RWNsSUdoMlPXMx16iLdRuSkAgvv\",\n  \"object\":
        \"chat.completion\",\n  \"created\": 1741113787,\n  \"model\": \"gpt-3.5-turbo-0125\",\n
        \ \"choices\": [\n    {\n      \"index\": 0,\n      \"message\": {\n        \"role\":
        \"assistant\",\n        \"content\": \"Foo\",\n        \"refusal\": null\n
        \     },\n      \"logprobs\": null,\n      \"finish_reason\": \"stop\"\n    }\n
        \ ],\n  \"usage\": {\n    \"prompt_tokens\": 14,\n    \"completion_tokens\":
        2,\n    \"total_tokens\": 16,\n    \"prompt_tokens_details\": {\n      \"cached_tokens\":
        0,\n      \"audio_tokens\": 0\n    },\n    \"completion_tokens_details\":
        {\n      \"reasoning_tokens\": 0,\n      \"audio_tokens\": 0,\n      \"accepted_prediction_tokens\":
        0,\n      \"rejected_prediction_tokens\": 0\n    }\n  },\n  \"service_tier\":
        \"default\",\n  \"system_fingerprint\": null\n}\n"
    headers:
      CF-RAY:
      - 91b38473fc2542c3-EWR
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Tue, 04 Mar 2025 18:43:07 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=ZMaRKmu2UrFo8ev7ZAbNxOeU2GvzZftzkBWDz1VGsLQ-1741113787-1.0.1.1-roCAwVcOy9Z._zwZGRPzImdlNDwBlmm07aN85pfNjrR3DnTKopD2WQ75dHZCZCev7m4CtgTVUirWjcUU9AwF4rLWEkS4BJbKJkccf2_noBs;
        path=/; expires=Tue, 04-Mar-25 19:13:07 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=AO790ERhWyducDX1T4B4Wh36OEis52NBcxBhCUKlUDA-1741113787915-0.0.1.1-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      content-length:
      - '767'
      openai-organization:
      - langchain
      openai-processing-ms:
      - '261'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '50000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '49999991'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_e588ff1732ba81dac14f5ca7dd59bfd9
    status:
      code: 200
      message: OK
version: 1
