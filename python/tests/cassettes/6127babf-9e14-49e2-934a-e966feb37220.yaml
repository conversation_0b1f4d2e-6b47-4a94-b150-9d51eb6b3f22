interactions:
- request:
    body: '{"input": [[9642], [9642]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkka
        Ye+1vrX57//48+efd14Xl+Gff/3551H1wz//+f3tmg3ZP//681//8efPnz///fv8fyuLZ15cr9Wr
        /C3/baxe12L6519/xP/95f8W/evPP0oPCr3k97QbBzlxYSnXOts24aqbBvncQOQpPvMf7dTNl5Lb
        cN4uW2ZfzUcyGlIpa8w7GHjyLms0WlwXYM+YhmczxLV4Dm8ygjmeCV7EPJjU22xqUYxtYhy2t2Aq
        l5kASXq4MWdtapy+DvqonYgzMGd1fyZcV9RU/Qjpm5hS6gXjUayP8Nj4N6o81GM9VceLqB6uiwUV
        vPMT9fvkUyFbPT7wbE41Hxv90cBCxyEeA2GH5qOj+GDHVo+lVON8Wu+9GAWLa8/W5bazuMTUPciP
        Z07IfGgttstiQR3ZsmR6/+ytPi3UErnibOHV3baTcTDKCmYF90S/KS8+jhHpkWCAT65k4MF4csQY
        hOVDo8rxKFlU6IwL+G9tS9VDZXKxeNYNeDZ7UVhvTM5eY/KGcXi+qCZvtt10Ll4Y0LnQmIEfn/zd
        rd0Lip0cYy0b1gn31PqJCipZVNmgszWnh9aGK416EqYODUYcjIXsbUygaFYf9Wg2OEKP8zkl1q5s
        Lep2YghGxVJmaO8yGadwqpB6fA946vL6ez/mPfAbjihfn/b5uBHOAmwf7ocFZHFNOompR2AqQ8Ss
        rkkwmU1IYXhlI9mu7WPN7+eXqm2NS0anzemUj4swDVElQYgPx+MlnwLvXqDHxr2Ra7t78PF1CGR4
        XBdPOg7BPZmK/c4FeZvcmblYoYCag0Ph+zzwaldafOXIRxfAN/fEeaVRPe7qRgBzu/OJpyz7bvJV
        2sNnGYfMXhhmJ639OUWdaHlka+J7MGaeQcENDE67o91xengMJZQfc8a7fGXWg3DXelWKeIGV+X3v
        eLl0bOgKR6dKUuo1X57ARloHD2JVs5WPnjrrqHDFG1nzqcvfa0MR/p4PB92ARsfXKhAC50mwtZeT
        uVkdbGQqUYIFV5y6QRZkF143SyYhUDdYJZ9AhlPIUyxXLOK83NxMtB7cJcFXuQvmt0l92GnRxNyu
        Fet5s/RKKLbZQMxL0AejOSQNetxpTLZd9smnWFiJ8MCmycySZcmYqlEPURW8SRgqVjA9Wu4vdBkb
        jJS3qub661OhWZtVEmLTqZe7uk6RcLcD4lBjyKdVkFO01KOaednQ1ZRtPF3Rm+uR6X6kBN0zvWYo
        3XsrRuaDH0yLUc7UOImOeO8v3WB6iO4FXsYBM3d5t/JxkHMXsjz28GxYgTUK2qEA+LgH/DjMOJia
        1bGFOSEuwVqTJb0hlSpI69QlacUiRNteixGf5ZKkVHbq8duvsOkAY3jeWzTslhCh8zJ6MDKcFzlj
        Z6eB8DQ/ifG8pcl4y3RbO7Odjpfhqs1Zo59cUMzzhnmzFSY8k9o9+tYXzZPZrlfCfW5U4e4ETBe1
        ez1v2ztFLnlfmX/dW8FS0HIVHlftSbxz87a4rsyZZgsyZ9vTLUnGDoc6ks5Ixb9+b+U+Ef7q61Yy
        b8GbjFkI+ZqXxLmZVTCWwkeCZFemBJ/Eupvf5tMHUitPFmiHqhvAN0Lo9orLCN4J+fytd3U71gt6
        QHXQcXhkFayM/ZLs171dzwcjmGEnugeszFafzDU9V2hHdMac+jkmrYhVB2WCkNJ8O0hJv+kNG85X
        c4MVC3RL+tRTD+ftqmWB0Al8TMqtDspWlJlDn2bNbpv3DC20LdUehzzgyHYE9FsfhkptjdWt6yGD
        zqJl/wwDDpc1hpaXGCO2EXLWUAdDbwwicwNNSDg740ZdePs3O6P6kY+SuevhbJ4VPGvGAzF7niuU
        R4dkkJXyktDMFUVoeYWJf2Fb/vC2Xqru98f373g5/ezeEhqbnGHxVIUBa/LlETI181mg1GYuCXfR
        R/4UbRi+Jwc0mdL1iYzT3sS8G17WEH+iEZT73cWjtzl0f/utoarBtvejGXzryQFybwdC9seQj2fV
        EWC7OlfM3IY6ooKWHdHdSCLciq2F+myoi189/rbz1d3WJW2im4pCErTWJO3vJrLqqSXOdf9JRvWj
        q/C2vYIZ0drs+szzehiE7Yr4evFJ5t1y7tVPLUrktFk/OXvcqx4+zoISk7E+6S4fzVa7wtbJ2n/U
        1jwPMkbLc7rA9+dNzimaFk+0iwqHEKVaWn3x7Br43g9yCKhTT6ez9YTyph7IhvUKYguN6KqXFvHP
        r5L+mXYS3D/1QNblXCacBJUJUjQVzM3vBeq3fHbhiSvt9/z4fNK6UR03L4uQQRDr4T0WM8qmbUzF
        rlXR3HhUR/l6KvFw2Ib5nE56pAlwK4kr+7q1cqNLgaKm0Oivfjgx9z26SKsZr3koWXNXnkf0vR68
        grNgUfVYu6AJpCC21A7BMBjXEDF2fBBvc2r5KGjXAr3v+pME02RaHB1vItwb7YbnoN/Ug5EbPno/
        niJW3cjruG+9MKxcnBJzSR41jUtPBdkpMixw75pQvljtwdn0b7LxqFXPTKR70F7piZhiO+bzHMcm
        lPp8IF65tdEgtjsR2BN7JLw+NwHf0yn+6R8d004Khk01Y6RbzoUQvCvyQUURhmRWYqbflA1nEpuP
        yOmyFcPm2AbzpOY9zJdMJ2Qa92g+rKTs188sfLU06XleXVAbOzKxk7HjfaMPDSi9oLBwhTH68QHo
        YSiTjfsorbGQoQD7HnoEm8GeD5+aR+jwfmLyvd8Be9zbHm3SbU3cN0/zaby5EuxE/8DcxaPtZu7Z
        FIhZ9eTHl5xLM0YdRjXWnG1mdZfP0lYrX7NYseSGxQ9JYkOz33HmG+apo5JihOpPj1VOUzRPBwW0
        22B9iHe6netR/bgybI0iozIZ191cBGYMz9Mno0oTary/lgKGSyS3WIBc4rR52BjkOnGIrYddx7Zr
        eMPYnBmmT07Rz39VL3j5bL1etwF/vVIbamHfkkDtH3w+iu8QBHtrEVOgq3r8uGUKMmkk4opAA34t
        2YwkTziSkF1oPayNMYUqW9vMgUubzNK11tXLZZ9jkcohH4t93KMu2qtkK/cm53qxD6EwBGD6irzr
        MZPk+fedLngx1IwPvazWF7Sn4qsz+Gi6kwS7wh0x+izuqC/PwlF9vfYzc92KIy73WQZaWYoMP4le
        T4NfVdqshD3me7TnfAuTCffCPZFwxe1g2dmaD/vbeP7ygdmtHP9lo7dz4SR0omc+DomSwV3ufRLI
        5xPnkhlEsDxnC7I9qkLQc28jgpJmHTHv8pXPnc2fGnWqiapLPAc0VS0JhMlgRA8Pr5qvnoOJdoU/
        YtU6rOsR2dcU7NjoyWaa3smkj4rwq2e2/vp1v977InTWdcZau3ugqRSOJXoZJ0y8m8QCOjqLGK2j
        HaXP6SJ/+/0QIgvsGq/Gt825Z8kCJL0y4BqmktNsKI9IZ2iDH9HHr+mvX1dlIf3tD16elTcKbQth
        fnFvfPjx0M9fnFc61ryM+iNqr9WHLtCyDvgqwCLKT1hiBqE94rVeyoCk3YeZ7jJIZlj1gtrr8u7L
        43ousm5QEZO8gemfVLW++0uAl5+G/fiWizyO0EK4bPHk4ATNZD+qf/mi8LSlxbJY/7d+W52749P1
        xI4/3qAqXJqAc0nFylrf+1T8pBxNm0oNUfPWC2KeItda8aGRtZE34Zc/zGDcnOUQos17x46Wklnf
        PADw2C99LBuHN+rh8RZAVY+AkVKbyarJl3vo5ySgg4kNa7yc1BbIu4qIfoi6nJ6LoYUwQhZVVqMQ
        TBe+rmBD1g7bFIsD4qv9xUXf/mFYoDYa5Tov4VsvxA7Ox4QvyE2AwCAd0dn1nc8r0xbBNdAJPxfJ
        sx5iYSGBuN9lWPC0QzA1NHFg5YYpsWr05IN1umNUt68FRp+sr6eT9jhCKL5iElQTQaN/dCrQ3PhI
        MPeuOUeTZKrN5CNmao99MH/98S8fWrvSD/jhUQuglZVI1nm8sQZPHQtN0dfzXx4cpgMqwTiYAx1f
        3duaIAlHkEcfU27jCPFSyDP111/bUa26iet+D7UyX7Cc3wvOjgT5gKl0wZXomBYfLnIMydiemRm9
        go4XBs/QrtFkOpsjoNYu6xB9vMRi68NW5n0er2MIosxljhsxxHns9GoYKRadBXqquVmvJPj2A7Fw
        LAefJ9kLP15gtl3frae8/ETo+38pLB5NPvPFYg+5f6Bs0w06n8/W5gJpMa+JqyV1x6O6jNE+NhLm
        WIpqzdUxiUG51y5dnKU1HzFxWoRUOSH+tx7nR6s8IdgLRzzdUfv1NxVDmIQp8apbx6cyUjB88y7z
        5rfRiZ9Fbv7yA+W/fkXbq4hKXCyY/vPHgFr4l/eJq35uvP/lZ/1s63T88vrIukGGw1VbMKO2t/XY
        xMIFfvpsvN454tL1foHb+5qzwF/p6Ju3XNBhJriThSEYyzpoAaSlzjZOOdXdQhlFYO14YrfrsuCT
        N9UuzPZ6Q9xT9UBcxPJ3XhD25Ku33XJ7vwI0ZNaJw9gz+F0PWr2OZywOvmiN70BqYJs9pS+PVgn1
        ps8TGXIPxDiVQ8BELJewslofi3rY1Uy1I6oVwvQiRjk/u8/B2B5/PE5yPdcsZu6ooPD29cTPWAm/
        eV+pQHaHhGHpRHIOidfCl2+Z+ejWQZ+Uax2qQykR86rYSDIby//55c8/0Vz2tgNeeonxEtVdPS3G
        MQViiwZxzaLPmRyJIVwfbkTn6sotrtRrG1T94zHHHKWOohulQFuik29+7ebLaZjhfVI9+prVjo/c
        IyJqZedC1plX13PmgqQuT5sWS9FTyKd08iQl0z+IWHsk8vlJkky5XlZHZtn8GbBd34l/rz8oFqQb
        9485g3O9IiS8ydSiU9i0igrv3XZU10XQO1cnhfOu+e6PI97JfZaqCTg+Xubxy+LbdFsqy9O6xbfh
        fMv/8tllThrcHjsnnw/+Zg+pJjssGd+dNRweL+eXJ/DYOTEaBacS4eAVWyxc5cAassU8gp3jhrgD
        syzu39YZbE6pzr58FXA9tI5wq5UtnuEydl/+NZFnFhQL4trNZzNbSmiK6ZaR0aqD2TsGmfrZrnbE
        1cN1MojQUvVNHoTQRZxYUxODim4QOuQS7QfEhssYI08PzsTg066bfvn+L5/rI6/Zz99//R2+nNL6
        8TcE0iFiDqe3gH7qiYLeHRW2XdtSR4fHMoMFO4bM+OaH/rK+z9Cj/EW2n3785oWgQbtzUxG7a1U+
        OcazBdn21swvrk43L9pFr3bRUf3qfcPH4WlGYHRLEZfLO+Hcn44RKLcIMzMypfzTfwoVfedXVNvm
        YfDtl6fq77MFC2b10c0d1lVoUvlKzMg8JmN3Oh9BlW2Lff0i4YfL8Q3nekmwHJs94vtLmWq/fprm
        N7WGztZc5bc+eKlJMi9SKqFimw4kpLKeSxZ4Kazjl8UCp+wSxpxlD73iUmbiQ5P3umUXWmrIAgkX
        OrHmX72/aHggTrw/WbOIVRuMzxNR5JRBPgvOFdAyvl/wyl9tginLd6b2ux+2XRvBcptmKaSa6rAw
        Sw/B6C7Dt+Lp3vk778PB+6iNrVYvQ8z8RXxO2KVUKjXWJPebj0prVNF5RmyrEPLN66jzprcOiiIa
        xDJ04a++qriNMLmZ5SPoH04sgAVOzZz3fZ8zU8oK1WuHgLl6+EimT40idDj5MjE20NfzllilcjJS
        i+7Dg5Hw73wEYvrp/p0/NtXYAk/GGyHPUEyGo/iZ4fVpN1T0umvH9HFw0I8vouAMNSdBa6IrjXtC
        zHId8CmsHTiuMxWfSH7IeSkkKfryM7Hxy02+PNbCt/6pKkGLmBtdLmiOBIKF+gFo7G2hB1MCAWt3
        NcrF/rR5wrMi228e8TuuvtULKgg12fra0IQXgUvRV99YcnEXaHwVZwz3SXwRw+GfYPrOd5Cdhw1J
        u/Ut/85XY4jrrcocsfUDNBixrUm4avDi/naCeZumImih2DBvVN98FNdKC6rHHxTyu1xTc6dGQJ1y
        YmFv2MFy4dwqdBjcC1nX9ivpB+NdgRzob+KtbUAD5lREc+Z0xF/yV843588IX71i+JXaweiwU/x3
        3vU9nrV8F1sZbYzsQpWXlQWTfkhmUISM4La5Y2tykkWJeq0HjHAc1Oz1Sp3fPJKisJn5XL17X/XN
        xQfzakuDHpLIQVNI33RqpkXNz4diRJfisac/PhsX91UM86G6Yu1xQMH0LrYq0izq0tkMac1XgSOh
        K9JOLEBLLxnLPgJo6iKi6OtX/WdXC5DOlwMWVuWMGumJCng2VcVItvBynrkoRtMpytnV17/z4mel
        K96qSdh+YVT19OMHLux3xE2dfcLLzcmEV4d0vCy3QdBv215CCjgqs0J9Z4mH5DbKc4k4/fqBRY0h
        uvzlJX/JNzkVtHivbj6twowdflojvt8q2N/mM9tmwzofHX9ZoYMUK8QhhdHRGJUOxOPaJMYRnfh4
        uDwctSC9yRzmR4g/yFmEZVxfiOfgV94Wz/KiLfNOxmos36wp81xX/fIe0xsxQOO1FELI5XdIsnsC
        1rzaX3uIC7qhstet8mEjRClcHZN89TupV5tNpMKw3OQsYJsimbcwP0EOzDdbL1QjUIF+Mvi+/yFB
        OrzrUZ3DAu5StiT+w7Hyv/NaNRVMuvz679iVexUiplLmCaEZiOXmUYA39iNxzPCd02C1xqBVksdI
        1w85bygO4XOLX3gpHs5omr3UBFxnHfOp8bKaNbub6Jsv2enxjuvZWNz3f+cxG7RkHWPdB8Nznd2J
        rocTmmu9LdQ+8XziOksDSd4tpeozmlYEM7/JOVF2tnZ4N5iFnpNzziLL1NZRQsl3Pt193ydlcAqn
        9C8P0nirmFD5C4t5xrpGbCqQAJE+U+ZIacVH75jGKszRTHQZspoPcthDSl5vLAm+ZVHniSX0PT+z
        z0Swvtd3QRu/lInubZZdv2IrE/bwWuM6bBb56Dxz+m8/tMt3Pgup3CCBjku2O+vPZCZB94Rv3mX4
        JFr1VNmBgLBKZroA7cbZpZwq2Mm0ZmvxcOZffX1CJz9OjKRDlc/9duWrYqzfmb9jqO43vWf/9b9T
        qPuIgy7PEA2vM00uw8Gii0P0Bn9Tr4i3zefvPP2tImRdzyyUWr1bjd3tAhNdV8xu76t8ZCIPoW4f
        C2KHh3vCJDOIwYguNwxetwvmT9ZnMIz1lgTxxwjG2JYb+OrXl6+Hbt7LrgvhMPrEj/cj7+TMK+Hy
        lDHxU21bjy8T6fCd91DNmVn+5YM3wMc/MMNNqmS2PmMFoPU1M/fnjs9xaaia9OQmsY6foe6bizXK
        0DQblj40E/Wr4BqpFf3EZHuYsq4T70aqlvz508Mm6L1bIqqWwjFG1fZe00JWjpAsw9ffvDNLiocB
        vwQFv8n4qOddP2RwUbwNM/R7FUw//3LC6H8AAAD//+zcW4+iMBQH8Pf5FBNf2QkISsu8uaCOXAQZ
        Rdlks/ECXhCqWAo2me++KZrNPu/j5rySEJIT0vb8zy9Vq57od8T8wkFG/zLz+6nKrEaJJxjf75Ed
        fG/nX1LpZbhXVVpFVTO1yjyOYux+n5a+rJdxWTtktX7OH1HXsh77n1hvq1CsNzex3iBZSqaBp+CC
        M67ePaPwatmXD9YMNWH4eTSUuEtYEKX1vDnH10+kj6YrZuFmjwjfXBM8iYjpL8X8ppKUkYnP+WzP
        PPmczemU1xle7HbDCkllgvjYKcZ6m29kClbmdbg+xPhMHYuNmnS4qS9hKeEfA3vAhpOjbtViPzBE
        /8b8Wd4gWkTOApFxwtt8mNMbYnWbX/iNp9XPvDrWzBPzqPNxYrG3eean/mevxxnpr7y2/6qknPvo
        xgy1xsoyZxX6uPi8tsnFwTWRjfa8u2m2Vxvj7dSWmMmdlUs0Jpv6ojfQ2Dg6Xtz6rk0IEv1Z4E6p
        umlUM54YxT4NqpWjFS5P2HGL2vPgUsWhpYn9rB/K+TgYy5qCyvCUHFHAolX7v5Y8G6QTvQiGi8rI
        k6S84So74rZe9yApeH1YEgfbQ1oEH7PVYN4tfWfbb/vB4dpY8iaU7RyZqiQxlyect/NBpEbRjQ2L
        sVc2on4YzZYHZl9Dz9Vce5HjqTQf+T3XSDZ17B2u+OqN5mJ+vkNUivUJnqeBWenHbXoS9a3w2cxD
        9kHU7NSU9+yZHzoiL32832lVwNe3fxAFXRAFIApAFIAoAFEAogBEAYgCEAUgCkAUgCgAUQCiAEQB
        iAIQBSAKQBSAKABRAKIARAGIAhAFIApAFIAoAFEAogBEwX8uCl5eX3+2dxbkZJecBQygSUPf/lCB
        t/Vu/aYo6uNig+q23ied96dA6FxKkl/oL0qypLh13l/VBzXoUELX578ev4gPfb38BgAA//8DAIpg
        UmUwQQAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c06e916429-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=6t3x1PWP1isudSReizi41egXSRGS2KStJWhVmtVyJDk-**********-*******-q9QdIvltEU8IPe70V1Q3wm.xXQ3KCRwrzhG0QP8IkFE0Ao13hyOIkIpLfhrMJvdft0.Y.B75sSa51amQt_mm9g;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=6m97vMafHqngwFlTL4inl.QL0il9RySGGdjNivGJGN8-**********351-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '26'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9998'
      x-ratelimit-remaining-tokens:
      - '9999998'
      x-ratelimit-reset-requests:
      - 7ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_f45dabf40222c4a01a3481700d0d9ba4
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [9642]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkkF
        Ye+1vrXzv//jz59/3nldXIZ//vXnn0fVD//85/ezazZk//zrz3/9x58/f/789+/1/60snnlxvVav
        8rf8d7B6XYvpn3/9Ef/3k/9b9K8//yg9KPSS39NuHOTEhaVc62zbhKtuGuRzA5Gn+Mx/tFM3X0pu
        w3m7bJl9NR/JaEilrDHvYODJu6zRaHFdgD1jGp7NENfiObzJCOZ4JngR82BSb7OpRTG2iXHY3oKp
        XGYCJOnhxpy1qXH6OuijdiLOwJzV/ZlwXVFT9SOkb2JKqReMR7E+wmPj36jyUI/1VB0vonq4LhZU
        8M5P1O+TT4Vs9fjAsznVfGz0RwMLHYd4DIQdmo+O4oMdWz2WUo3zab33YhQsrj1bl9vO4hJT9yA/
        njkh86G12C6LBXVky5Lp/bO3+rRQS+SKs4VXd9tOxsEoK5gV3BP9prz4OEakR4IBPrmSgQfjyRFj
        EJYPjSrHo2RRoTMu4L+1LVUPlcnF4lk34NnsRWG9MTl7jckbxuH5opq82XbTuXhhQOdCYwZ+fPJ3
        t3YvKHZyjLVsWCfcU+snKqhkUWWDztacHlobrjTqSZg6NBhxMBaytzGBoll91KPZ4Ag9zueUWLuy
        tajbiSEYFUuZob3LZJzCqULq8T3gqcvr7/2Y98BvOKJ8fdrn40Y4C7B9uB8WkMU16SSmHoGpDBGz
        uibBZDYhheGVjWS7to81v59fqrY1LhmdNqdTPi7CNESVBCE+HI+XfAq8e4EeG/dGru3uwcfXIZDh
        cV086TgE92Qq9jsX5G1yZ+ZihQJqDg6F7/PAq11p8ZUjH10A39wT55VG9birGwHM7c4nnrLsu8lX
        aQ+fZRwye2GYnbT25xR1ouWRrYnvwZh5BgU3MDjtjnbH6eExlFB+zBnv8pVZD8Jd61Up4gVW5ve9
        4+XSsaErHJ0qSanXfHkCG2kdPIhVzVY+euqso8IVb2TNpy5/rw1F+Hs+HHQDGh1fq0AInCfB1l5O
        5mZ1sJGpRAkWXHHqBlmQXXjdLJmEQN1glXwCGU4hT7FcsYjzcnMz0XpwlwRf5S6Y3yb1YadFE3O7
        VqznzdIrodhmAzEvQR+M5pA06HGnMdl22SefYmElwgObJjNLliVjqkY9RFXwJmGoWMH0aLm/0GVs
        MFLeqprrr0+FZm1WSYhNp17u6jpFwt0OiEONIZ9WQU7RUo9q5mVDV1O28XRFb65HpvuREnTP9Jqh
        dO+tGJkPfjAtRjlT4yQ64r2/dIPpIboXeBkHzNzl3crHQc5dyPLYw7NhBdYoaIcC4OMe8OMw42Bq
        VscW5oS4BGtNlvSGVKogrVOXpBWLEG17LUZ8lkuSUtmpx2+/wqYDjOF5b9GwW0KEzsvowchwXuSM
        nZ0GwtP8JMbzlibjLdNt7cx2Ol6GqzZnjX5yQTHPG+bNVpjwTGr36FtfNE9mu14J97lRhbsTMF3U
        7vW8be8UueR9Zf51bwVLQctVeFy1J/HOzdviujJnmi3InG1PtyQZOxzqSDojFf/6vZX7RPirr1vJ
        vAVvMmYh5GteEudmVsFYCh8Jkl2ZEnwS625+m08fSK08WaAdqm4A3wih2ysuI3gn5PO33tXtWC/o
        AdVBx+GRVbAy9kuyX/d2PR+MYIad6B6wMlt9Mtf0XKEd0Rlz6ueYtCJWHZQJQkrz7SAl/aY3bDhf
        zQ1WLNAt6VNPPZy3q5YFQifwMSm3OihbUWYOfZo1u23eM7TQtlR7HPKAI9sR0G99GCq1NVa3rocM
        OouW/TMMOFzWGFpeYozYRshZQx0MvTGIzA00IeHsjBt14e3f7IzqRz5K5q6Hs3lW8KwZD8Tsea5Q
        Hh2SQVbKS0IzVxSh5RUm/oVt+cPbeqm63x/fv9/L6Wf3ltDY5AyLpyoMWJMvj5Cpmc8CpTZzSbiL
        PvKnaMPwPTmgyZSuT2Sc9ibm3fCyhvgTjaDc7y4evc2h+9tvDVUNtr0fzeBbTw6QezsQsj+GfDyr
        jgDb1bli5jbUERW07IjuRhLhVmwt1GdDXfzq8Xecr+62LmkT3VQUkqC1Jml/N5FVTy1xrvtPMqof
        XYW37RXMiNZm12ee18MgbFfE14tPMu+Wc69+alEip836ydnjXvXwcRaUmIz1SXf5aLbaFbZO1v6j
        tuZ5kDFantMFvj9vck7RtHiiXVQ4hCjV0uqLZ9fA936QQ0CdejqdrSeUN/VANqxXEFtoRFe9tIh/
        fpX0z7ST4P6pB7Iu5zLhJKhMkKKpYG5+L1C/5bMLT1xpv+fH55PWjeq4eVmEDIJYD++xmFE2bWMq
        dq2K5sajOsrXU4mHwzbM53TSI02AW0lc2detlRtdChQ1hUZ/9cOJue/RRVrNeM1DyZq78jyi7/Xg
        FZwFi6rH2gVNIAWxpXYIhsG4hoix44N4m1PLR0G7Fuh9158kmCbT4uh4E+HeaDc8B/2mHozc8NH7
        8RSx6kZex33rhWHl4pSYS/KoaVx6KshOkWGBe9eE8sVqD86mf5ONR616ZiLdg/ZKT8QU2zGf5zg2
        odTnA/HKrY0Gsd2JwJ7YI+H1uQn4nk7xT//omHZSMGyqGSPdci6E4F2RDyqKMCSzEjP9pmw4k9h8
        RE6XrRg2xzaYJzXvYb5kOiHTuEfzYSVlv35m4aulSc/z6oLa2JGJnYwd7xt9aEDpBYWFK4zRjw9A
        D0OZbNxHaY2FDAXY99Aj2Az2fPjUPEKH9xOT7/0O2OPe9miTbmvivnmaT+PNlWAn+gfmLh5tN3PP
        pkDMqic/vuRcmjHqMKqx5mwzq7t8lrZa+ZrFiiU3LH5IEhua/Y4z3zBPHZUUI1R/eqxymqJ5Oiig
        3QbrQ7zT7VyP6seVYWsUGZXJuO7mIjBjeJ4+GVWaUOP9tRQwXCK5xQLkEqfNw8Yg14lDbD3sOrZd
        wxvG5swwfXKKfv6resHLZ+v1ug3465XaUAv7lgRq/+DzUXyHINhbi5gCXdXjxy1TkEkjEVcEGvBr
        yWYkecKRhOxC62FtjClU2dpmDlzaZJauta5eLvsci1QO+Vjs4x510V4lW7k3OdeLfQiFIQDTV+Rd
        j5kkz7/3dMGLoWZ86GW1vqA9FV+dwUfTnSTYFe6I0WdxR315Fo7q67WfmetWHHG5zzLQylJk+En0
        ehr8qtJmJewx36M951uYTLgX7omEK24Hy87WfNjfxvOXD8xu5fgvG72dCyehEz3zcUiUDO5y75NA
        Pp84l8wgguU5W5DtURWCnnsbEZQ064h5l6987mz+1KhTTVRd4jmgqWpJIEwGI3p4eNV89RxMtCv8
        EavWYV2PyL6mYMdGTzbT9E4mfVSEXz2z9dev+/XeF6GzrjPW2t0DTaVwLNHLOGHi3SQW0NFZxGgd
        7Sh9Thf52++HEFlg13g1vm3OPUsWIOmVAdcwlZxmQ3lEOkMb/Ig+fk1//boqC+lvf/DyrLxRaFsI
        84t748OPh37+4rzSseZl1B9Re60+dIGWdcBXARZRfsISMwjtEa/1UgYk7T7MdJdBMsOqF9Rel3df
        HtdzkXWDipjkDUz/pKr1/b4EePlp2I9vucjjCC2EyxZPDk7QTPaj+pcvCk9bWiyL9X/rt9W5Oz5d
        T+z44w2qwqUJOJdUrKz1vU/FT8rRtKnUEDVvvSDmKXKtFR8aWRt5E375wwzGzVkOIdq8d+xoKZn1
        zQMAj/3Sx7JxeKMeHm8BVPUIGCm1mayafLmHfk4COpjYsMbLSW2BvKuI6Ieoy+m5GFoII2RRZTUK
        wXTh6wo2ZO2wTbE4IL7aX1z07R+GBWqjUa7zEr71QuzgfEz4gtwECAzSEZ1d3/m8Mm0RXAOd8HOR
        POshFhYSiPtdhgVPOwRTQxMHVm6YEqtGTz5YpztGdftaYPTJ+no6aY8jhOIrJkE1ETT6R6cCzY2P
        BHPvmnM0SabaTD5ipvbYB/PXH//yobUr/YAfHrUAWlmJZJ3HG2vw1LHQFH09/+XBYTqgEoyDOdDx
        1b2tCZJwBHn0MeU2jhAvhTxTf/21HdWqm7ju91Ar8wXL+b3g7EiQD5hKF1yJjmnx4SLHkIztmZnR
        K+h4YfAM7RpNprM5Amrtsg7Rx0sstj5sZd7n8TqGIMpc5rgRQ5zHTq+GkWLRWaCnmpv1SoJvPxAL
        x3LweZK98OMFZtv13XrKy0+Evv+XwuLR5DNfLPaQ+wfKNt2g8/lsbS6QFvOauFpSdzyqyxjtYyNh
        jqWo1lwdkxiUe+3SxVla8xETp0VIlRPif+txfrTKE4K9cMTTHbVff1MxhEmYEq+6dXwqIwXDN+8y
        b34bnfhZ5OYvP1D+61e0vYqoxMWC6T9/DKiFf3mfuOrnxvtfftbPtk7HL6+PrBtkOFy1BTNqe1uP
        TSxc4KfPxuudIy5d7xe4va85C/yVjr55ywUdZoI7WRiCsayDFkBa6mzjlFPdLZRRBNaOJ3a7Lgs+
        eVPtwmyvN8Q9VQ/ERSx/5wVhT7562y239ytAQ2adOIw9g9/1oNXreMbi4IvW+A6kBrbZU/ryaJVQ
        b/o8kSH3QIxTOQRMxHIJK6v1saiHXc1UO6JaIUwvYpTzs/scjO3xx+Mk13PNYuaOCgpvX0/8jJXw
        m/eVCmR3SBiWTiTnkHgtfPmWmY9uHfRJudahOpQSMa+KjSSzsfyfX/78E81lbzvgpZcYL1Hd1dNi
        HFMgtmgQ1yz6nMmRGML14UZ0rq7c4kq9tkHVPx5zzFHqKLpRCrQlOvnm126+nIYZ3ifVo69Z7fjI
        PSKiVnYuZJ15dT1nLkjq8rRpsRQ9hXxKJ09SMv2DiLVHIp+fJMmU62V1ZJbNnwHb9Z349/qDYkG6
        cf+YMzjXK0LCm0wtOoVNq6jw3m1HdV0EvXN1Ujjvmu/3ccQ7uc9SNQHHx8s8fll8m25LZXlat/g2
        nG/5Xz67zEmD22Pn5PPB3+wh1WSHJeO7s4bD4+X88gQeOydGo+BUIhy8YouFqxxYQ7aYR7Bz3BB3
        YJbF/ds6g80p1dmXrwKuh9YRbrWyxTNcxu7LvybyzIJiQVy7+WxmSwlNMd0yMlp1MHvHIFM/29WO
        uHq4TgYRWqq+yYMQuogTa2piUNENQodcov2A2HAZY+TpwZkYfNp10y/f/+VzfeQ1+/n7r7/Dl1Na
        P/6GQDpEzOH0FtBPPVHQu6PCtmtb6ujwWGawYMeQGd/80F/W9xl6lL/I9tOP37wQNGh3bipid63K
        J8d4tiDb3pr5xdXp5kW76NUuOqpfvW/4ODzNCIxuKeJyeSec+9MxAuUWYWZGppR/+k+hou/8imrb
        PAy+/fJU/X22YMGsPrq5w7oKTSpfiRmZx2TsTucjqLJtsa9fJPxwOb7hXC8JlmOzR3x/KVPt10/T
        /KbW0Nmaq/zWBy81SeZFSiVUbNOBhFTWc8kCL4V1/LJY4JRdwpiz7KFXXMpMfGjyXrfsQksNWSDh
        QifW/Kv3Fw0PxIn3J2sWsWqD8XkiipwyyGfBuQJaxvcLXvmrTTBl+c7UfvfDtmsjWG7TLIVUUx0W
        ZukhGN1l+FY83Tt/5304eB+1sdXqZYiZv4jPCbuUSqXGmuR+81FpjSo6z4htFUK+eR113vTWQVFE
        g1iGLvzVVxW3ESY3s3wE/cOJBbDAqZnzvu9zZkpZoXrtEDBXDx/J9KlRhA4nXybGBvp63hKrVE5G
        atF9eDAS/p2PQEw/3b/zx6YaW+DJeCPkGYrJcBQ/M7w+7YaKXnftmD4ODvrxRRScoeYkaE10pXFP
        iFmuAz6FtQPHdabiE8kPOS+FJEVffiY2frnJl8da+NY/VSVoEXOjywXNkUCwUD8Ajb0t9GBKIGDt
        rka52J82T3hWZPvNI37H1bd6QQWhJltfG5rwInAp+uobSy7uAo2v4ozhPokvYjj8E0zf+Q6y87Ah
        abe+5d/5agxxvVWZI7Z+gAYjtjUJVw1e3N9OMG/TVAQtFBvmjeqbj+JaaUH1+INCfpdrau7UCKhT
        TizsDTtYLpxbhQ6DeyHr2n4l/WC8K5AD/U28tQ1owJyKaM6cjvhL/sr55vwZ4atXDL9SOxgddor/
        zru+v2ct38VWRhsju1DlZWXBpB+SGRQhI7ht7tianGRRol7rASMcBzV7vVLnN4+kKGxmPlfv3ld9
        c/HBvNrSoIckctAU0jedmmlR8/OhGNGleOzpj8/GxX0Vw3yorlh7HFAwvYutijSLunQ2Q1rzVeBI
        6Iq0EwvQ0kvGso8AmrqIKPr6Vf/Z1QKk8+WAhVU5o0Z6ogKeTVUxki28nGcuitF0inJ29fXvvPhZ
        6Yq3ahK2XxhVPf34gQv7HXFTZ5/wcnMy4dUhHS/LbRD027aXkAKOyqxQ31niIbmN8lwiTr9+YFFj
        iC5/eclf8k1OBS3eq5tPqzBjh5/WiO+3Cva3+cy22bDOR8dfVuggxQpxSGF0NEalA/G4NolxRCc+
        Hi4PRy1IbzKH+RHiD3IWYRnXF+I5+JW3xbO8aMu8k7EayzdryjzXVb+8x/RGDNB4LYUQcvkdkuye
        gDWv9tce4oJuqOx1q3zYCFEKV8ckX/1O6tVmE6kwLDc5C9imSOYtzE+QA/PN1gvVCFSgnwy++z8k
        SId3PapzWMBdypbEfzhW/ndeq6aCSZdf/x27cq9CxFTKPCE0A7HcPArwxn4kjhm+cxqs1hi0SvIY
        6foh5w3FIXxu8QsvxcMZTbOXmoDrrGM+NV5Ws2Z3E33zJTs93nE9G4v7/u88ZoOWrGOs+2B4rrM7
        0fVwQnOtt4XaJ55PXGdpIMm7pVR9RtOKYOY3OSfKztYO7waz0HNyzllkmdo6Sij5zqe7735SBqdw
        Sv/yII23igmVv7CYZ6xrxKYCCRDpM2WOlFZ89I5prMIczUSXIav5IIc9pOT1xpLgWxZ1nlhC3/Mz
        +0wE63t9F7TxS5no3mbZ9Su2MmEPrzWuw2aRj84zp//2Q7t857OQyg0S6Lhku7P+TGYSdE/45l2G
        T6JVT5UdCAirZKYL0G6cXcqpgp1Ma7YWD2f+1dcndPLjxEg6VPncb1e+Ksb6nfk7hup+03v2X/87
        hbqPOOjyDNHwOtPkMhwsujhEb/A39Yp423z+ztPfKkLW9cxCqdW71djdLjDRdcXs9r7KRybyEOr2
        sSB2eLgnTDKDGIzocsPgdbtg/mR9BsNYb0kQf4xgjG25ga9+ffl66Oa97LoQDqNP/Hg/8k7OvBIu
        TxkTP9W29fgykQ7feQ/VnJnlXz54A3z8AzPcpEpm6zNWAFpfM3N/7vgcl4aqSU9uEuv4Geq+uVij
        DE2zYelDM1G/Cq6RWtFPTLaHKes68W6kasmfPz1sgt67JaJqKRxjVG3vNS1k5QjJMnz9zTuzpHgY
        8EtQ8JuMj3re9UMGF8XbMEO/V8H08y8njCQqf/POd//CR5ryibByk5g1iakLMM8Hjxi//S+hCxuQ
        KV3RQTJvVvdMDykExrbDC7VLu9F/n7J/7z+ipWX99b+v3tLdV2/6r96ghVBsSSjCizMuzaH2CscF
        XtytCE27XVxpYrp8M3K4jcn0SNsYqevtiVkwlejN87YA9/A28fG7f0MFcW3C4xmVLFw8mv8BAAD/
        /+zazY6bMBAH8HfplUpkIcHmiAghYAiE5bO3QKABFkgI2MTSvnuFmyforZIfwJeRpZn5zy+cTpS0
        MLpejRkIYwmoiXpTYflGu4GbkASXWwa/JrTHh6UycnIPRgH+0mwNG1at7MnaD9R1f8PeuVvA1Mco
        AoNZUpYP0+kJMGH5hbe4Mnnn1ZmsN9id0LHBmZu/81Pvc7uleNilLtu/ZqGjHnhiVSJwk3R4Bse7
        R4k93BEkg6iyeTdfiocNYXGyBaxTlDqDjEVdibaajM24vjvkJVsDWPcz3zlNUr5Iemap/e/Kn1Mk
        9w4tcV0ANg8mEgz28trPdoHYmb4pyhswBk1ZAx/HKfuvI221ylJ634hmtSvL8QnntoasXi+/7Cm5
        JQOCtjH1/vGcauHH6KFix/ZB46ImdAlEuwO6JAjYoSWl7D4IpDh+YqM33XFZ6wfBOblh+xG4juzY
        UQdPQnjwto5a5iRzbw/4cA/hej+/gknIFAuGla/PSl1UzVrfGX7pXYCPg9Q2y/hq3/khWvPSv+9/
        MBXw/fMfRMEHFwVcFHBRwEUBFwVcFHBRwEUBFwVcFHBRwEUBFwX/oSj4AwAA///s3LEKgCAYReHd
        pxB3IRx9lwghaShT6g9aevcoI1rbz3qX+wIfB1GAKEAUIAoQBYgCRAGiAFGAKEAUIAoQBYgCRAGi
        AFGAKPgjCpTW7d0sSLmP0wUDJO5iXypgQx9s07gaNtjWMETjH4FgypJTkU7yGOfVeO0qNTCSJUyf
        WV1HhzoBAAD//wMAimBSZTBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c0890a967f-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=LOzleZdmcRHq_k8ivJmSpKH3A1nC2QEG.jr6QfrBEVE-**********-*******-XMxrEqRrp6jU6HBLC37zKUYMf76POCBfVMhd7JHzJdWfjlsl0Nef8FqH6zmPKoWklvMvsMaxeV1UHCa4Ip2l5g;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=JbDwNhs8Wa1lIHBiAsSY.Kcld.sWpwINV.ZrNaCgWz4-**********362-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '18'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9994'
      x-ratelimit-remaining-tokens:
      - '9999247'
      x-ratelimit-reset-requests:
      - 33ms
      x-ratelimit-reset-tokens:
      - 4ms
      x-request-id:
      - req_b5115f65d90a7b52d62d54c99b2929df
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [2822]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkkF
        Ye+1vrXzv//jz59/3nldXIZ//vXnn0fVD//85/ezazZk//zrz3/9x58/f/789+/1/60snnlxvVav
        8rf8d7B6XYvpn3/9Ef/3k/9b9K8//yg9KPSS39NuHOTEhaVc62zbhKtuGuRzA5Gn+Mx/tFM3X0pu
        w3m7bJl9NR/JaEilrDHvYODJu6zRaHFdgD1jGp7NENfiObzJCOZ4JngR82BSb7OpRTG2iXHY3oKp
        XGYCJOnhxpy1qXH6OuijdiLOwJzV/ZlwXVFT9SOkb2JKqReMR7E+wmPj36jyUI/1VB0vonq4LhZU
        8M5P1O+TT4Vs9fjAsznVfGz0RwMLHYd4DIQdmo+O4oMdWz2WUo3zab33YhQsrj1bl9vO4hJT9yA/
        njkh86G12C6LBXVky5Lp/bO3+rRQS+SKs4VXd9tOxsEoK5gV3BP9prz4OEakR4IBPrmSgQfjyRFj
        EJYPjSrHo2RRoTMu4L+1LVUPlcnF4lk34NnsRWG9MTl7jckbxuH5opq82XbTuXhhQOdCYwZ+fPJ3
        t3YvKHZyjLVsWCfcU+snKqhkUWWDztacHlobrjTqSZg6NBhxMBaytzGBoll91KPZ4Ag9zueUWLuy
        tajbiSEYFUuZob3LZJzCqULq8T3gqcvr7/2Y98BvOKJ8fdrn40Y4C7B9uB8WkMU16SSmHoGpDBGz
        uibBZDYhheGVjWS7to81v59fqrY1LhmdNqdTPi7CNESVBCE+HI+XfAq8e4EeG/dGru3uwcfXIZDh
        cV086TgE92Qq9jsX5G1yZ+ZihQJqDg6F7/PAq11p8ZUjH10A39wT55VG9birGwHM7c4nnrLsu8lX
        aQ+fZRwye2GYnbT25xR1ouWRrYnvwZh5BgU3MDjtjnbH6eExlFB+zBnv8pVZD8Jd61Up4gVW5ve9
        4+XSsaErHJ0qSanXfHkCG2kdPIhVzVY+euqso8IVb2TNpy5/rw1F+Hs+HHQDGh1fq0AInCfB1l5O
        5mZ1sJGpRAkWXHHqBlmQXXjdLJmEQN1glXwCGU4hT7FcsYjzcnMz0XpwlwRf5S6Y3yb1YadFE3O7
        VqznzdIrodhmAzEvQR+M5pA06HGnMdl22SefYmElwgObJjNLliVjqkY9RFXwJmGoWMH0aLm/0GVs
        MFLeqprrr0+FZm1WSYhNp17u6jpFwt0OiEONIZ9WQU7RUo9q5mVDV1O28XRFb65HpvuREnTP9Jqh
        dO+tGJkPfjAtRjlT4yQ64r2/dIPpIboXeBkHzNzl3crHQc5dyPLYw7NhBdYoaIcC4OMe8OMw42Bq
        VscW5oS4BGtNlvSGVKogrVOXpBWLEG17LUZ8lkuSUtmpx2+/wqYDjOF5b9GwW0KEzsvowchwXuSM
        nZ0GwtP8JMbzlibjLdNt7cx2Ol6GqzZnjX5yQTHPG+bNVpjwTGr36FtfNE9mu14J97lRhbsTMF3U
        7vW8be8UueR9Zf51bwVLQctVeFy1J/HOzdviujJnmi3InG1PtyQZOxzqSDojFf/6vZX7RPirr1vJ
        vAVvMmYh5GteEudmVsFYCh8Jkl2ZEnwS625+m08fSK08WaAdqm4A3wih2ysuI3gn5PO33tXtWC/o
        AdVBx+GRVbAy9kuyX/d2PR+MYIad6B6wMlt9Mtf0XKEd0Rlz6ueYtCJWHZQJQkrz7SAl/aY3bDhf
        zQ1WLNAt6VNPPZy3q5YFQifwMSm3OihbUWYOfZo1u23eM7TQtlR7HPKAI9sR0G99GCq1NVa3rocM
        OouW/TMMOFzWGFpeYozYRshZQx0MvTGIzA00IeHsjBt14e3f7IzqRz5K5q6Hs3lW8KwZD8Tsea5Q
        Hh2SQVbKS0IzVxSh5RUm/oVt+cPbeqm63x/fv9/L6Wf3ltDY5AyLpyoMWJMvj5Cpmc8CpTZzSbiL
        PvKnaMPwPTmgyZSuT2Sc9ibm3fCyhvgTjaDc7y4evc2h+9tvDVUNtr0fzeBbTw6QezsQsj+GfDyr
        jgDb1bli5jbUERW07IjuRhLhVmwt1GdDXfzq8Xecr+62LmkT3VQUkqC1Jml/N5FVTy1xrvtPMqof
        XYW37RXMiNZm12ee18MgbFfE14tPMu+Wc69+alEip836ydnjXvXwcRaUmIz1SXf5aLbaFbZO1v6j
        tuZ5kDFantMFvj9vck7RtHiiXVQ4hCjV0uqLZ9fA936QQ0CdejqdrSeUN/VANqxXEFtoRFe9tIh/
        fpX0z7ST4P6pB7Iu5zLhJKhMkKKpYG5+L1C/5bMLT1xpv+fH55PWjeq4eVmEDIJYD++xmFE2bWMq
        dq2K5sajOsrXU4mHwzbM53TSI02AW0lc2detlRtdChQ1hUZ/9cOJue/RRVrNeM1DyZq78jyi7/Xg
        FZwFi6rH2gVNIAWxpXYIhsG4hoix44N4m1PLR0G7Fuh9158kmCbT4uh4E+HeaDc8B/2mHozc8NH7
        8RSx6kZex33rhWHl4pSYS/KoaVx6KshOkWGBe9eE8sVqD86mf5ONR616ZiLdg/ZKT8QU2zGf5zg2
        odTnA/HKrY0Gsd2JwJ7YI+H1uQn4nk7xT//omHZSMGyqGSPdci6E4F2RDyqKMCSzEjP9pmw4k9h8
        RE6XrRg2xzaYJzXvYb5kOiHTuEfzYSVlv35m4aulSc/z6oLa2JGJnYwd7xt9aEDpBYWFK4zRjw9A
        D0OZbNxHaY2FDAXY99Aj2Az2fPjUPEKH9xOT7/0O2OPe9miTbmvivnmaT+PNlWAn+gfmLh5tN3PP
        pkDMqic/vuRcmjHqMKqx5mwzq7t8lrZa+ZrFiiU3LH5IEhua/Y4z3zBPHZUUI1R/eqxymqJ5Oiig
        3QbrQ7zT7VyP6seVYWsUGZXJuO7mIjBjeJ4+GVWaUOP9tRQwXCK5xQLkEqfNw8Yg14lDbD3sOrZd
        wxvG5swwfXKKfv6resHLZ+v1ug3465XaUAv7lgRq/+DzUXyHINhbi5gCXdXjxy1TkEkjEVcEGvBr
        yWYkecKRhOxC62FtjClU2dpmDlzaZJauta5eLvsci1QO+Vjs4x510V4lW7k3OdeLfQiFIQDTV+Rd
        j5kkz7/3dMGLoWZ86GW1vqA9FV+dwUfTnSTYFe6I0WdxR315Fo7q67WfmetWHHG5zzLQylJk+En0
        ehr8qtJmJewx36M951uYTLgX7omEK24Hy87WfNjfxvOXD8xu5fgvG72dCyehEz3zcUiUDO5y75NA
        Pp84l8wgguU5W5DtURWCnnsbEZQ064h5l6987mz+1KhTTVRd4jmgqWpJIEwGI3p4eNV89RxMtCv8
        EavWYV2PyL6mYMdGTzbT9E4mfVSEXz2z9dev+/XeF6GzrjPW2t0DTaVwLNHLOGHi3SQW0NFZxGgd
        7Sh9Thf52++HEFlg13g1vm3OPUsWIOmVAdcwlZxmQ3lEOkMb/Ig+fk1//boqC+lvf/DyrLxRaFsI
        84t748OPh37+4rzSseZl1B9Re60+dIGWdcBXARZRfsISMwjtEa/1UgYk7T7MdJdBMsOqF9Rel3df
        HtdzkXWDipjkDUz/pKr1/b4EePlp2I9vucjjCC2EyxZPDk7QTPaj+pcvCk9bWiyL9X/rt9W5Oz5d
        T+z44w2qwqUJOJdUrKz1vU/FT8rRtKnUEDVvvSDmKXKtFR8aWRt5E375wwzGzVkOIdq8d+xoKZn1
        zQMAj/3Sx7JxeKMeHm8BVPUIGCm1mayafLmHfk4COpjYsMbLSW2BvKuI6Ieoy+m5GFoII2RRZTUK
        wXTh6wo2ZO2wTbE4IL7aX1z07R+GBWqjUa7zEr71QuzgfEz4gtwECAzSEZ1d3/m8Mm0RXAOd8HOR
        POshFhYSiPtdhgVPOwRTQxMHVm6YEqtGTz5YpztGdftaYPTJ+no6aY8jhOIrJkE1ETT6R6cCzY2P
        BHPvmnM0SabaTD5ipvbYB/PXH//yobUr/YAfHrUAWlmJZJ3HG2vw1LHQFH09/+XBYTqgEoyDOdDx
        1b2tCZJwBHn0MeU2jhAvhTxTf/21HdWqm7ju91Ar8wXL+b3g7EiQD5hKF1yJjmnx4SLHkIztmZnR
        K+h4YfAM7RpNprM5Amrtsg7Rx0sstj5sZd7n8TqGIMpc5rgRQ5zHTq+GkWLRWaCnmpv1SoJvPxAL
        x3LweZK98OMFZtv13XrKy0+Evv+XwuLR5DNfLPaQ+wfKNt2g8/lsbS6QFvOauFpSdzyqyxjtYyNh
        jqWo1lwdkxiUe+3SxVla8xETp0VIlRPif+txfrTKE4K9cMTTHbVff1MxhEmYEq+6dXwqIwXDN+8y
        b34bnfhZ5OYvP1D+61e0vYqoxMWC6T9/DKiFf3mfuOrnxvtfftbPtk7HL6+PrBtkOFy1BTNqe1uP
        TSxc4KfPxuudIy5d7xe4va85C/yVjr55ywUdZoI7WRiCsayDFkBa6mzjlFPdLZRRBNaOJ3a7Lgs+
        eVPtwmyvN8Q9VQ/ERSx/5wVhT7562y239ytAQ2adOIw9g9/1oNXreMbi4IvW+A6kBrbZU/ryaJVQ
        b/o8kSH3QIxTOQRMxHIJK6v1saiHXc1UO6JaIUwvYpTzs/scjO3xx+Mk13PNYuaOCgpvX0/8jJXw
        m/eVCmR3SBiWTiTnkHgtfPmWmY9uHfRJudahOpQSMa+KjSSzsfyfX/78E81lbzvgpZcYL1Hd1dNi
        HFMgtmgQ1yz6nMmRGML14UZ0rq7c4kq9tkHVPx5zzFHqKLpRCrQlOvnm126+nIYZ3ifVo69Z7fjI
        PSKiVnYuZJ15dT1nLkjq8rRpsRQ9hXxKJ09SMv2DiLVHIp+fJMmU62V1ZJbNnwHb9Z349/qDYkG6
        cf+YMzjXK0LCm0wtOoVNq6jw3m1HdV0EvXN1Ujjvmu/3ccQ7uc9SNQHHx8s8fll8m25LZXlat/g2
        nG/5Xz67zEmD22Pn5PPB3+wh1WSHJeO7s4bD4+X88gQeOydGo+BUIhy8YouFqxxYQ7aYR7Bz3BB3
        YJbF/ds6g80p1dmXrwKuh9YRbrWyxTNcxu7LvybyzIJiQVy7+WxmSwlNMd0yMlp1MHvHIFM/29WO
        uHq4TgYRWqq+yYMQuogTa2piUNENQodcov2A2HAZY+TpwZkYfNp10y/f/+VzfeQ1+/n7r7/Dl1Na
        P/6GQDpEzOH0FtBPPVHQu6PCtmtb6ujwWGawYMeQGd/80F/W9xl6lL/I9tOP37wQNGh3bipid63K
        J8d4tiDb3pr5xdXp5kW76NUuOqpfvW/4ODzNCIxuKeJyeSec+9MxAuUWYWZGppR/+k+hou/8imrb
        PAy+/fJU/X22YMGsPrq5w7oKTSpfiRmZx2TsTucjqLJtsa9fJPxwOb7hXC8JlmOzR3x/KVPt10/T
        /KbW0Nmaq/zWBy81SeZFSiVUbNOBhFTWc8kCL4V1/LJY4JRdwpiz7KFXXMpMfGjyXrfsQksNWSDh
        QifW/Kv3Fw0PxIn3J2sWsWqD8XkiipwyyGfBuQJaxvcLXvmrTTBl+c7UfvfDtmsjWG7TLIVUUx0W
        ZukhGN1l+FY83Tt/5304eB+1sdXqZYiZv4jPCbuUSqXGmuR+81FpjSo6z4htFUK+eR113vTWQVFE
        g1iGLvzVVxW3ESY3s3wE/cOJBbDAqZnzvu9zZkpZoXrtEDBXDx/J9KlRhA4nXybGBvp63hKrVE5G
        atF9eDAS/p2PQEw/3b/zx6YaW+DJeCPkGYrJcBQ/M7w+7YaKXnftmD4ODvrxRRScoeYkaE10pXFP
        iFmuAz6FtQPHdabiE8kPOS+FJEVffiY2frnJl8da+NY/VSVoEXOjywXNkUCwUD8Ajb0t9GBKIGDt
        rka52J82T3hWZPvNI37H1bd6QQWhJltfG5rwInAp+uobSy7uAo2v4ozhPokvYjj8E0zf+Q6y87Ah
        abe+5d/5agxxvVWZI7Z+gAYjtjUJVw1e3N9OMG/TVAQtFBvmjeqbj+JaaUH1+INCfpdrau7UCKhT
        TizsDTtYLpxbhQ6DeyHr2n4l/WC8K5AD/U28tQ1owJyKaM6cjvhL/sr55vwZ4atXDL9SOxgddor/
        zru+v2ct38VWRhsju1DlZWXBpB+SGRQhI7ht7tianGRRol7rASMcBzV7vVLnN4+kKGxmPlfv3ld9
        c/HBvNrSoIckctAU0jedmmlR8/OhGNGleOzpj8/GxX0Vw3yorlh7HFAwvYutijSLunQ2Q1rzVeBI
        6Iq0EwvQ0kvGso8AmrqIKPr6Vf/Z1QKk8+WAhVU5o0Z6ogKeTVUxki28nGcuitF0inJ29fXvvPhZ
        6Yq3ahK2XxhVPf34gQv7HXFTZ5/wcnMy4dUhHS/LbRD027aXkAKOyqxQ31niIbmN8lwiTr9+YFFj
        iC5/eclf8k1OBS3eq5tPqzBjh5/WiO+3Cva3+cy22bDOR8dfVuggxQpxSGF0NEalA/G4NolxRCc+
        Hi4PRy1IbzKH+RHiD3IWYRnXF+I5+JW3xbO8aMu8k7EayzdryjzXVb+8x/RGDNB4LYUQcvkdkuye
        gDWv9tce4oJuqOx1q3zYCFEKV8ckX/1O6tVmE6kwLDc5C9imSOYtzE+QA/PN1gvVCFSgnwy++z8k
        SId3PapzWMBdypbEfzhW/ndeq6aCSZdf/x27cq9CxFTKPCE0A7HcPArwxn4kjhm+cxqs1hi0SvIY
        6foh5w3FIXxu8QsvxcMZTbOXmoDrrGM+NV5Ws2Z3E33zJTs93nE9G4v7/u88ZoOWrGOs+2B4rrM7
        0fVwQnOtt4XaJ55PXGdpIMm7pVR9RtOKYOY3OSfKztYO7waz0HNyzllkmdo6Sij5zqe7735SBqdw
        Sv/yII23igmVv7CYZ6xrxKYCCRDpM2WOlFZ89I5prMIczUSXIav5IIc9pOT1xpLgWxZ1nlhC3/Mz
        +0wE63t9F7TxS5no3mbZ9Su2MmEPrzWuw2aRj84zp//2Q7t857OQyg0S6Lhku7P+TGYSdE/45l2G
        T6JVT5UdCAirZKYL0G6cXcqpgp1Ma7YWD2f+1dcndPLjxEg6VPncb1e+Ksb6nfk7hup+03v2X/87
        hbqPOOjyDNHwOtPkMhwsujhEb/A39Yp423z+ztPfKkLW9cxCqdW71djdLjDRdcXs9r7KRybyEOr2
        sSB2eLgnTDKDGIzocsPgdbtg/mR9BsNYb0kQf4xgjG25ga9+ffl66Oa97LoQDqNP/Hg/8k7OvBIu
        TxkTP9W29fgykQ7feQ/VnJnlXz54A3z8AzPcpEpm6zNWAFpfM3N/7vgcl4aqSU9uEuv4Geq+uVij
        DE2zYelDM1G/Cq6RWtFPTLaHKes68W6kasmfPz1sgt67JaJqKRxjVG3vNS1k5QjJMnz9zTuzpHgY
        8EtQ8JuMj3re9UMGF8XbMEO/V8H08y8njCQqf/POd//CR5ryibByk5g1iakLMM8Hjxi//S+hCxuQ
        KV3RQTJvVvdMDykExrbDC7VLu9F/n7J/7z+ipWX99b+v3tLdV2/6r96ghVBsSSjCizMuzaH2CscF
        XtytCE27XVxpYrp8M3K4jcn0SNsYqevtiVkwlejN87YA9/A28fG7f0MFcW3C4xmVLFw8mv8BAAD/
        /5xaS9OyMJf8L7NlqkBuCUvkfjNRbuJOFBEUkUsCpGr++5TP+y1nNUuKyqZP55zuzknmA1teML3f
        LQK4sQLMCT6O+pdvvAQoJMvp+izgew5Maq8Pq1y+p5GDF93XqeU1qrn85oH2828UHbsVzJ8sSEHv
        VOwvH2bzBOjyl1+gNZKW/+TVhWS0NJoDt6VFVP4nP0WxLDPaK+foz38RrmMITFQTFyjkHSXA/SK2
        +P03gEvPa396t1xvgw/h7eBz1GDBOewlyhtqKusSdbLmGy6b5PXg589weJjFchWNwtM+9QOTcyB9
        QlbR5gb+9GAuwpMp/eaZcuI7Bzu8JIDx1FYNwDQ7//F1ZC/94akfbKVE66pqnCB5NfAPrw1XH7Y8
        8z6AvjV/sHs868luRMFN+fOD1lXL2Xri/Q4YIsfRkFWM/b0PAjHLJmp9nGhcf/hBcMyf1B9OUSiF
        ftrBA5fYSA61qlyK6DnAIbKT3/v5HcxcoXoweWCDqM3t0f7wJfBtdCfq9uKrXcft9Z/8MPjlpf/O
        /9ffVsD//Pf/Y6Ng939vFLRK8EFK4ScluQSKDtt5A9QGFIdbvZ9ucK99S2qOuA9XqAc1mE9ySfFL
        aczlNQsLjN/vK9nuZ7ftI6WOoR2lFXYXwWtFbPsv0HJGhb2h5sBqxkMHZQ1B7FqXOOzY49VBXmUB
        kaxLbK4pnDyY4G6k3tn/sjU04RW+JXePQ/XiASZ/Tp2mSuBGdg/VCBc+QFdVsVNC3X7BbIu+tgyT
        9NbiyDzV5nqWvQlOwDex+T6MjE7XKIDJJb+SXRXb5oIFKVCvamHTcvqqyWa/rwIU0rTGx686JttO
        CBBQAvSmxtoAk+rzEsH5sn2JDNOuZEHXG+B0WUwELzJNlpLMAZDBi8dIyxsw9XDZAJjjC0WiiMqh
        9qwjMHJ9IaJrGuZO+/gFRKlICcEJDbcRxAUIX2tM3ePTHHd3ddfBl9Uk1KVRWK7ffvDA8/b6UJS+
        bbCdxTKFO9A2SNprYjkMbinDd/EosXvKjJBwD/UKWHYQCGv5mW27vS/DKUpUxNe2ORIuVhdYcvZA
        944mmssiDFfw+08oX5/MJbSfC+D1VaGuJwXh1ny/DVRCsfnVawtnbe/9siWQYPtRN2DZjDrWroax
        Yq9iZrmQy4vTPvllhyPRBeM3a0dLjnx1Iq2ldmDtPdNRVyVQsXPVJTDHKbXgc/0peu49mGuQZz2c
        l3pPzzix222T5wE8n9UDyRNU2+1++aQwsKc9ftx2fkl4RRnUH38wSt9v1ts9lWGfq4Bah5c/Stzr
        JcBrK5/wvZKLkcXyUYXKTAyyBOWLkW07CJAVd0K4stRbctrqGL4c74gP+z0u101XZZhc4RlplaWG
        fU8VDwDJ8Kg5jyb7suvmQbArQiRJx5f5afRdDAaFozg89SNYJPlewWvYQBxK2myulvKsYBuXRyQp
        sT4yBEQPsuJBMErOLhC/DBbQsPwjtk6oNRfqmgTUC3/ByFircu3mQweRSDB1Ry9u1662IRyPxMX4
        eplHJk4AAd/MSmxowzTS0hx0uDajQybDNJI6Yt4NGkp9QJ+neAs3dDIN/ok7jihFG7fL5eo28A04
        GQH+47TSVLcQClN1ItKPD4IrzgToKP/gIOqbdr3vswry/HdPQ3kNwWZJ0IHXonkTa+f2YHJwJ0Dn
        bk4IDIdqXA0+tKCSwBR7BzMFFH9TGYiGeUbP3dUPJylON1iJR4XqefNopw++OlB8GDyOUHA3mTft
        exjYZI8fVzaBZddKoppJ9RlXkTuz5e4PPbwVDkSqrh7L1af5BA+Px0o0VhnhMhVkARHVHXweSFJu
        U3NJ/90Ptb/jdpScrQb1kYrUud3Y+PG27w1c0vVNZmYZTHyd9RhyvnCh+966sM1V5EqNS2kkQAnH
        cDs3TIVQpTORlSpM1khSVVgY1RUtxjomjGb1pDJ5PtFfPZO1U20Eg8ML08BpDuUAT/AF+7ofcLG7
        buP3c5gITF6Tg44PITeJBHoEM37wqFOi3UjlWtnASdcz6nPWrlw5pKcwN1STjFJ7AFtr1gKwYyHB
        WRot4YakkEBhup0oUp2+XYJxqOCYcHvCy8dzuK0hguAskxGxYeexlZHLC97t1KP4wqOE3qS+BpcJ
        5DTi+I0tPhdO0FvcJzW7/ZDQ89NpoBv+novuqwO2/s11qli0ITVfl0OybL5nqWboBWR5xaRcACmu
        qu/vMA4EXk9Yo2sxcFPjg1Gfq+aix2UHe22I6T4e+3J7lu0EL3JioBWXxPzHJ3CSf46nEtpp8XxZ
        FXfLGePTUyxpgeajyp9PM3XeJAjXp1UY4LmPIFpc/msuEdMrkCleSlO6i8MluasVMHXDoJEKriad
        06ZWqQAx9Y3caFfBO8rw8LivRObrz7hM4SeAEm7sH/5Zya64raA/OBs2n22cbOCcI1i1Oo+97DkB
        2riZAxEn6gQcdcKoNjgV5K09o95OWNkcSZuqVc+LQRbjHJnUHuwjfFqVSJ5sGxmz/A6pv/tO+NNT
        TMjuY7z+ziOpGa7jV1NrAZ7rl4ofZ+3EfvgWMH5/rji6pWNILAss0Bn2GdH2uwNb5YH0oLOnDw4v
        8TuZfVoRYN0fGUbOXh6nlLvU0FPeE3ad3aEdLpqSw6chKgjcPD1ZvOF1VD/yKaN+fYjM9ZsZRANL
        neLiEjols9t3BY72WtOoyd1kuX3NGormmBKl4n/1k8cYfN1ij++uGbbLVHQbyOalp3u8+znUXs2B
        MAgpWZ7iNs41XKG2l/zPX39l0ydbVbAcLJ0GbSiD5dOsNRxAN/3q47ZMSZccfjd+R+q9NjGyRbYH
        cu4h4v13ysGW91kN0Ji3GB9MLlnVKOQAllcJm7BX2v66Rzfw7NKcRrvkHk56AQpwzeKRmmfzztZP
        GtWge+UHHDJFKLuszFTYxpcjEcelS+YKsAVGWVygJr4zxhK7eEFrcgA22zcaN8dWILQWjqGzKFrg
        fagUEVyhYaDi3H7Nn746/vXrf/xbsnE5qt0g7PGtilE7/fXrpMtNwrjoztbSkSxwOT1HutcsYxRK
        w+q1/XyRqXfb78tN3OlXzZVqi7ro4YZfGt85gFzJwf4w1wnl7RD+9S9qlDRI3urbNqCaljxRT2Kd
        rNGaOPCcYYS0V4ATVk/nDkYHr0Ncyr9N0m8igsZ6vSEuDbt2flA1VY/B9/zjxxSyVxarUOZEBf/4
        UK6lwztAn+ABH37zeftwfA8sIZppuSQJ+C5vrQCU5TEi2eVVLvd8iGEveC4NlTA0p8rydW0e9Aif
        D2PfLtFU9ypdSUZE/eO039W5NeBlqDmNBr1mKzwYAXxS8Untp1ebW26kAugR8zGCnh1SweI80PdL
        idbeUgCrlTOCTp18KE4eF7CU4LWBx0n2kPZMhWT53C4E+L6E//FlFko5/cMPe/G4AuY7HgSGGCfY
        OnAeY7VTyfDd4ICsXKwnO+2zL6Ce3y7Uvlddu0294oF5HHz8p8+GwlcF4JgJRz7IdpnEVQcDunEx
        UEQ7wWRHX9DVp3UTkWS+k2Rm1y0AQNI9pHzuXTgt3l6GpnbYiFK9CVhj91NByddP2LNCbZzwlVf/
        9SPJ/lBGhjRpYKCLAaI/fSUW92v875s/mFy5Pm6xDAZO6PBx25pxRfLqaEN7A9hFSpewUgs8iN+t
        hv/m2SaWvgcx3K846PaMTfe8iTUEXg7G8vFsTtkoxxAcjysSNf/MSGv2IhTW3Ec74bEzh1bz4D89
        cVhF29xBZ8jhrY4IEgKCRuau7RHShyhg/b5dAMtV/FIH//HFtvDIwlUTfB1k2DdIL6JTyC79q4Hh
        4nHkm5wAW9FDNoDKlUe0K449mIfWa+DodRla9j5JxmMLjpCOqMZWLuomu+bhEWScEZKPeHu3hD+c
        Amifp4AmvH5L1gH6HfibDwspzJKVZ/8GzWhx6fkImp9+T1Igtw9E3XedjTR+KRWgju1Q49b6oeBz
        IYHdyUlQl0VKwsZfojFxhUZd9r8AAAD//0ycS++CQLbt5/0pOj0lHfABVfSMl4CAVQiImNzcAD4A
        ReRRBVRyvvsN/M89OVMHIqT2Xmv99pa67ZhBiQTE3jjQw6yLGbsbLySDi93QaAOsbKmfFkSbLEL7
        m1uHZEuaGjjwJlNHMSdA7UBLgL4dOMThgWTj0dVTqL46Rjg51timfp0gfCpag3Ur1jKmfJMEBIfr
        juLR4zK6+lXr6hfUNnio95qVxJDW8Q87Tl3rMy9ODVz9kqiWhtt621MJt23aYTNKUjCnXWLC2dVS
        ilf/FvqSAH78IC355gOoddBmkH2/W6odP7CjtGwiGKroh7GNn6xf6g3m0UbDi9/IxpJ/CoAv1Jza
        8/vajUselQ253xO+1L9u58eglcQrQkQSPqo7OrPmg3iiE+JvLgXksJ998BIt5++8sOfzyQG30T2s
        fI0x64Mc+iC89ncawByBcTAPJbxKUUMtF/esr5SHCcn8+ZEd9bpsetI5lq82/pA7TY8hFaN9BBc/
        g8RIFPUZvO8CPORvldoxbTtmng0PhppQ0wPH/7LJa4EP81svYu1+/VZkgEktbdukI/ugm9hcHp0H
        VMjboc9kGrsu/bVEOrpPD3FL3pxg+INAHo2Oap66D98P46jBs/k6Ls+PB6wP3zHcWWlEXRnrbFY1
        PoXurztix7SR3pwbuQf+UU+pU2/mbDp9bi1c6gmNbeRUZNDGXE5PtkhPv+TkjpfH2ILF/2L8GV4d
        G4bqDap3MJLnygs+KJNgWBlHrE6xH46e2Pig0Tv1Ly8veXOGWbsZqQY12s1ubgbgGL45bIiuWs3m
        7ZbCx2lZ2jwQBAQuxlsgnM2aCFq7YUNgffP1fJHRvhfhcEd3c73/tR+w2WksAp4fWyMcEilb/Qp8
        fXFLtTOO9dFTFBuaNzYv/WcHBu3jCiBqsg7JNP1lTN4hAxr+4KMNuGAw7zTbhEv+Q5uktXVGMi2S
        vNJX6bmHkz6KHrDB0l+wTsMX+xJQpKteYYcc+o6lo/aC+Zificg9oqqJdlMPJHCMseapSciQLQvA
        Em2VRsMdZ7Mx5YF0NPgEH5+3U7bwhREKz7xBv8uw60jl2C1ErqXg4+9QheSbcBzM2t2InYrfhdPp
        PCNANruKSE/AVd0p2gXwEqQyqRY+8nvmgSQXlZmTSaX7bGiIO0IvbDLswuyTjXmuS8DNuxcZ3ELv
        psvdDQAr8Z0UQKv1QQ/aN+wOUYa6yPh2bPHbcCp/JhLGsA1HExMBBgN1ieTUtUvZnUfwy24P7J5R
        3zFfzxHMw9MBL/klZAasX2B3kmx86LM8HLVa/Ms7BPDfupvmKIihkL4n7PG3WZ8CQiJ4ubQ7auWI
        d98kPDiQlac72rNk67Lfg9Zg+1k2fqz26bJgmkzp4PQFVaPBADTozj68uvcnVRULhERQ3B5alsaR
        tf9P4FkloMod7u+8/6BZRqv+UEfv54yKKHnBfWKV1Jkro2O/7osAOI4Q7cjcu+Pin4EEp/Avz1OL
        a6U1X2Gl2M4VK14PB9B33//p+8RSyZaC3UNCANc4Y8Hj1gBpJ+ZkZtKdke2y0botA4kqbdR28/RB
        Hlh4DkZ4IOH8AsYbEnohmPKSHzJ+6h9wyS/4udTn/HX5CLALFrD2OpVd03PfHLQ36pFN3HTV3EZZ
        CcIvp1A76M5sTBEyYP3MLzSXrxdG+c5rYZ3WD6qV/LsbVV4KAEMDTw+c6zA2WK0Enrc5xmY3miG7
        Gw2C1e7V/vHBXSZrDnzEakwNkX/pA+muCrRoElC85hVLPrxgxlktOce00CnUrTdMEg5hYxx+HX1v
        RAiOhpxQXN7rbATffQ0/wyWg6r7Vs8k42Rysv7WF+lPMVYTVXgpr7TGQPW/a4eYuyW+45f0Sq81+
        n/Vg+QcLaTYSgfpZcYWlfreYP3fUK8e6YkUYl1Ai7oC2avZxpyOMo1XfKPbDpKNO3ShwzYunudU7
        dhTVQD7Wwuq3i6xd/XY5ViO1RqGpxv0zSmTp/SvR5vkqWRNN5z24ZfqWtAKvZCx0xxQueYfa6c1w
        BR9xOdyQSiTSF5+7P15XfaThxEH7486sNlKZppxMlVFRqkEWVA2Cu7ZF8CLE1Rj6swDW6/Goy7JJ
        HgpPXngImlSahKP4Smvgmm6PT0UUhaRNWQz7gFEibOmHtTHZRFDU+4ma1l0Nh8mMXrB4oDd2pkvN
        BnK+zlBNXY7stElgRMu5BvDfyifj4g/n01hJcAOKkszb+NYR4wxSGBa5R5VCE9yh1X45uOu9S4Op
        zPSWP90cKEU3Hh/CZMrms6iY6/NFnR1vst/Sz6B2YCfS76td1t/vmgmra2gR5o5FRxXBV6B7y2oi
        qPneHXNaRfKij2R2ZT5jnHGYIec+YqoU26Aaz9pGg4kVn5GAkkZf/KojXkQnooafvrv5F15iMLki
        wTaN7tmoiOkDcraX47v56zMWS1YNr+H3jJr44mbij6pQbuQmwGjgru6YItMALXj39BB5oz4n98AH
        0wcoRKqzUJ9WfiiasPvLj2zlAdn7Aunaz6fD8NLgUq+Imxq/mq6P4wMs/gnb4XPM2BC1L/iIbjeq
        ScK5GzVlLEFyNhTqnpFXjRobt+B8m3UitsMrmy66p0lqBQZs2XeHzTfj5oFGuVnYFh9dRixzdOCE
        3YYe20EJd/49y8Gj0nh8mPVbxsKDX8OQoDMq75PJdp98v4f8t/BJsfcNfTp8FSSv+sByGgJGc6UE
        7Qur2HAiGnZh4HvAaB8cYXX2q4jBhXsIwrIj4OrbrG+O4wOO+05AR9nfgrnh5e3K58huv1ezJW++
        ASqEhMYm1ivBE5tAXM7j6m/Z3F/OPWigdsHufurYYNvPLRge7Ytw0usLfiEYOHG2xtdav9WGQ3b8
        1y8E3bjok1AmNtRRapBNprWgCX5gL7nTDKi2jW8VE3bcAwaHy446u6nMxl2Qj+B9TB18qsvA/W7m
        XADB0/og/v0sQd+7X3vVU6r64aGbmCoaoH4+Ltg4Sr5ec72kwNqK39j6SAKb1PM7hev5MURecbs+
        7COoi/weW/u9Gm5+pRbJyX17IHMk4oodTQWCNrse6anhTmx3lqUSNnPpUMOOO0ZX/7HwNmoverhZ
        8g5MtDzF3nuu9LnU2hZ+jrvN33l6RcaUgtAhAj3er2ed5ZwdSQ+na//0bvVnsJXRDzE9+FXd+v2L
        3lJVASIYEJ/soaPlFnbNaA/mguoOvCT7iboL/51Px48GlXNd0lOnODrdKWkPj11PsUokoeo+mtiu
        8yDsLH59+7roEkSJ+SOAnHfZJB3eNhSvHsKxNEB35Cucw+T50NY8nhHenGL5UigVuXsXLdsp3ySF
        u92G/+uHf/OoJX8u84/YHcbnpYaASDFdeePkGfMbrjxsqfeQbfZCs/LnhY/uXOLmyAd+kVBq1qVd
        bRPIbOAf2Iu8UdK4/d13HvAE7yk+Lv5s2t+pBnVR3mNr11YZHaQOiYZOvxR5xUln1wKVEDqBRvVC
        nrrS4DJpJ5HjQAYnPIBRmaVmzTuEG4VAH8vb05Zc3O3IftuMYf/+RnDNm9TwlU84crElwINDCuz6
        KXGX/lDChQ9jS5W3IT1UQw7UnfvF6uwgNsnfYwpXfyewg5dtBDT2a7+k7v7ad2PFHgm4RIq0PP9r
        Nq28SDG9LVa36NuxK0EOXPnZqqfDNzJeUIbCSE2+kLpZqq7Nyp/J7TJcq6mo9ntoVkWBwOkduuPz
        rDRwmQ8hgJKp+xZh/KcvVMOnW8WU11jDq3360EPi20yoCxbLl7J5LfXtMrryNEG7KxTzr8mlS94D
        tRW9MXIOUzhNNXgD0GpP6kqscXsfcQ+48MhFryK2/H5bmq35RaaZG0LWh30MVn7pBVxWMUXwNdnB
        yKNut8m6saGTIy/1jvYX0LuT5wYpWO9nuliGLqx+aeEFy0YoAn98Qp76huqKaYe1tpX3MNrcIhIE
        etRNV64p4VltAyTE25e+8PsYmlVVoFHu+Ipy1w+CxvMQ4L/zsn9No+wYpkHOy/OmnThokMLtHSsf
        s2dTCD4Q3kcRol0T+10/fEgKk9O7xOql0P9bTwdFAwv/+7qDXjsa8GLpslxfcTefEEVb5BEZMV6Q
        2HCJuxwqxfZAeN6cGPHoh0CUGD+qX3cwHN2gSOUwryOqk0Pl1s289WD7OqmIRPxnnUcmUH39GEaA
        HLq5Go+xtM6bl3kvmPlw5KA1zBds82YT0lPB7cHFFW2Mr/IEFn4bydpd4shc8u9Fr2UNPpxfi02d
        fcGs/bY+BOrPpYaaJ+4Qjy35qyfDIagaXB0mq36jbQ2/gG3mSJBvXXgjQnG5r+c/keivrrD1Mbfh
        knf2az7Gujuq3Wadv3RP84jV6/EVvlJkmkA7qhbZ0K9cDYU8OvCyK69oCs4/fX4WhxhaheIv/avQ
        J+2jC9KiL3jVm2lWZmnt52Txb/rsbQ8vMMepSdHH5NnvI1oK7PvqTrgmTt0lbxowB+8bdsjTBWu9
        gW3q2oSD5Tv7jf3Fg8u8lyJAPtWY564Ed0ft/DevJ1Y/bsHKLx1y8LpJ8nTu/28U/OOf//w/6zsL
        6ub++CyLAcNjGv79P6sC/07v6b8FYfv3YgPSp6/Hv/7z3xsI//p1Tf0b/u/QvB/f/l//+ef2b9Xg
        X0MzpJ//9fE/lgv91z/+HwAAAP//AwCM575JMEEAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c08895cec1-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=GjIa.U9Ux2eLgd2vhsSvnEXQtrIvsZMYw2Ft2JOAW24-**********-*******-HD9XZEKU6nJdM9A90MSP_0qEd.I.paprWpLaxKEWXzmcz2ysvIjXS6lxdh2_JpljXqXfYQDo_kvTVSM06BoHhw;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=JbDwNhs8Wa1lIHBiAsSY.Kcld.sWpwINV.ZrNaCgWz4-**********362-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '16'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9993'
      x-ratelimit-remaining-tokens:
      - '9999837'
      x-ratelimit-reset-requests:
      - 36ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_91c5a94c9be7fa8fbe52366d437b7ccd
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [9642]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkka
        Ye+1vrX57//48+efd14Xl+Gff/3551H1wz//+f3tmg3ZP//681//8efPnz///fv8fyuLZ15cr9Wr
        /C3/baxe12L6519/xP/95f8W/evPP0oPCr3k97QbBzlxYSnXOts24aqbBvncQOQpPvMf7dTNl5Lb
        cN4uW2ZfzUcyGlIpa8w7GHjyLms0WlwXYM+YhmczxLV4Dm8ygjmeCV7EPJjU22xqUYxtYhy2t2Aq
        l5kASXq4MWdtapy+DvqonYgzMGd1fyZcV9RU/Qjpm5hS6gXjUayP8Nj4N6o81GM9VceLqB6uiwUV
        vPMT9fvkUyFbPT7wbE41Hxv90cBCxyEeA2GH5qOj+GDHVo+lVON8Wu+9GAWLa8/W5bazuMTUPciP
        Z07IfGgttstiQR3ZsmR6/+ytPi3UErnibOHV3baTcTDKCmYF90S/KS8+jhHpkWCAT65k4MF4csQY
        hOVDo8rxKFlU6IwL+G9tS9VDZXKxeNYNeDZ7UVhvTM5eY/KGcXi+qCZvtt10Ll4Y0LnQmIEfn/zd
        rd0Lip0cYy0b1gn31PqJCipZVNmgszWnh9aGK416EqYODUYcjIXsbUygaFYf9Wg2OEKP8zkl1q5s
        Lep2YghGxVJmaO8yGadwqpB6fA946vL6ez/mPfAbjihfn/b5uBHOAmwf7ocFZHFNOompR2AqQ8Ss
        rkkwmU1IYXhlI9mu7WPN7+eXqm2NS0anzemUj4swDVElQYgPx+MlnwLvXqDHxr2Ra7t78PF1CGR4
        XBdPOg7BPZmK/c4FeZvcmblYoYCag0Ph+zzwaldafOXIRxfAN/fEeaVRPe7qRgBzu/OJpyz7bvJV
        2sNnGYfMXhhmJ639OUWdaHlka+J7MGaeQcENDE67o91xengMJZQfc8a7fGXWg3DXelWKeIGV+X3v
        eLl0bOgKR6dKUuo1X57ARloHD2JVs5WPnjrrqHDFG1nzqcvfa0MR/p4PB92ARsfXKhAC50mwtZeT
        uVkdbGQqUYIFV5y6QRZkF143SyYhUDdYJZ9AhlPIUyxXLOK83NxMtB7cJcFXuQvmt0l92GnRxNyu
        Fet5s/RKKLbZQMxL0AejOSQNetxpTLZd9smnWFiJ8MCmycySZcmYqlEPURW8SRgqVjA9Wu4vdBkb
        jJS3qub661OhWZtVEmLTqZe7uk6RcLcD4lBjyKdVkFO01KOaednQ1ZRtPF3Rm+uR6X6kBN0zvWYo
        3XsrRuaDH0yLUc7UOImOeO8v3WB6iO4FXsYBM3d5t/JxkHMXsjz28GxYgTUK2qEA+LgH/DjMOJia
        1bGFOSEuwVqTJb0hlSpI69QlacUiRNteixGf5ZKkVHbq8duvsOkAY3jeWzTslhCh8zJ6MDKcFzlj
        Z6eB8DQ/ifG8pcl4y3RbO7Odjpfhqs1Zo59cUMzzhnmzFSY8k9o9+tYXzZPZrlfCfW5U4e4ETBe1
        ez1v2ztFLnlfmX/dW8FS0HIVHlftSbxz87a4rsyZZgsyZ9vTLUnGDoc6ks5Ixb9+b+U+Ef7q61Yy
        b8GbjFkI+ZqXxLmZVTCWwkeCZFemBJ/Eupvf5tMHUitPFmiHqhvAN0Lo9orLCN4J+fytd3U71gt6
        QHXQcXhkFayM/ZLs171dzwcjmGEnugeszFafzDU9V2hHdMac+jkmrYhVB2WCkNJ8O0hJv+kNG85X
        c4MVC3RL+tRTD+ftqmWB0Al8TMqtDspWlJlDn2bNbpv3DC20LdUehzzgyHYE9FsfhkptjdWt6yGD
        zqJl/wwDDpc1hpaXGCO2EXLWUAdDbwwicwNNSDg740ZdePs3O6P6kY+SuevhbJ4VPGvGAzF7niuU
        R4dkkJXyktDMFUVoeYWJf2Fb/vC2Xqru98f373g5/ezeEhqbnGHxVIUBa/LlETI181mg1GYuCXfR
        R/4UbRi+Jwc0mdL1iYzT3sS8G17WEH+iEZT73cWjtzl0f/utoarBtvejGXzryQFybwdC9seQj2fV
        EWC7OlfM3IY6ooKWHdHdSCLciq2F+myoi189/rbz1d3WJW2im4pCErTWJO3vJrLqqSXOdf9JRvWj
        q/C2vYIZ0drs+szzehiE7Yr4evFJ5t1y7tVPLUrktFk/OXvcqx4+zoISk7E+6S4fzVa7wtbJ2n/U
        1jwPMkbLc7rA9+dNzimaFk+0iwqHEKVaWn3x7Br43g9yCKhTT6ez9YTyph7IhvUKYguN6KqXFvHP
        r5L+mXYS3D/1QNblXCacBJUJUjQVzM3vBeq3fHbhiSvt9/z4fNK6UR03L4uQQRDr4T0WM8qmbUzF
        rlXR3HhUR/l6KvFw2Ib5nE56pAlwK4kr+7q1cqNLgaKm0Oivfjgx9z26SKsZr3koWXNXnkf0vR68
        grNgUfVYu6AJpCC21A7BMBjXEDF2fBBvc2r5KGjXAr3v+pME02RaHB1vItwb7YbnoN/Ug5EbPno/
        niJW3cjruG+9MKxcnBJzSR41jUtPBdkpMixw75pQvljtwdn0b7LxqFXPTKR70F7piZhiO+bzHMcm
        lPp8IF65tdEgtjsR2BN7JLw+NwHf0yn+6R8d004Khk01Y6RbzoUQvCvyQUURhmRWYqbflA1nEpuP
        yOmyFcPm2AbzpOY9zJdMJ2Qa92g+rKTs188sfLU06XleXVAbOzKxk7HjfaMPDSi9oLBwhTH68QHo
        YSiTjfsorbGQoQD7HnoEm8GeD5+aR+jwfmLyvd8Be9zbHm3SbU3cN0/zaby5EuxE/8DcxaPtZu7Z
        FIhZ9eTHl5xLM0YdRjXWnG1mdZfP0lYrX7NYseSGxQ9JYkOz33HmG+apo5JihOpPj1VOUzRPBwW0
        22B9iHe6netR/bgybI0iozIZ191cBGYMz9Mno0oTary/lgKGSyS3WIBc4rR52BjkOnGIrYddx7Zr
        eMPYnBmmT07Rz39VL3j5bL1etwF/vVIbamHfkkDtH3w+iu8QBHtrEVOgq3r8uGUKMmkk4opAA34t
        2YwkTziSkF1oPayNMYUqW9vMgUubzNK11tXLZZ9jkcohH4t93KMu2qtkK/cm53qxD6EwBGD6irzr
        MZPk+fedLngx1IwPvazWF7Sn4qsz+Gi6kwS7wh0x+izuqC/PwlF9vfYzc92KIy73WQZaWYoMP4le
        T4NfVdqshD3me7TnfAuTCffCPZFwxe1g2dmaD/vbeP7ygdmtHP9lo7dz4SR0omc+DomSwV3ufRLI
        5xPnkhlEsDxnC7I9qkLQc28jgpJmHTHv8pXPnc2fGnWqiapLPAc0VS0JhMlgRA8Pr5qvnoOJdoU/
        YtU6rOsR2dcU7NjoyWaa3smkj4rwq2e2/vp1v977InTWdcZau3ugqRSOJXoZJ0y8m8QCOjqLGK2j
        HaXP6SJ/+/0QIgvsGq/Gt825Z8kCJL0y4BqmktNsKI9IZ2iDH9HHr+mvX1dlIf3tD16elTcKbQth
        fnFvfPjx0M9fnFc61ryM+iNqr9WHLtCyDvgqwCLKT1hiBqE94rVeyoCk3YeZ7jJIZlj1gtrr8u7L
        43ousm5QEZO8gemfVLW++0uAl5+G/fiWizyO0EK4bPHk4ATNZD+qf/mi8LSlxbJY/7d+W52749P1
        xI4/3qAqXJqAc0nFylrf+1T8pBxNm0oNUfPWC2KeItda8aGRtZE34Zc/zGDcnOUQos17x46Wklnf
        PADw2C99LBuHN+rh8RZAVY+AkVKbyarJl3vo5ySgg4kNa7yc1BbIu4qIfoi6nJ6LoYUwQhZVVqMQ
        TBe+rmBD1g7bFIsD4qv9xUXf/mFYoDYa5Tov4VsvxA7Ox4QvyE2AwCAd0dn1nc8r0xbBNdAJPxfJ
        sx5iYSGBuN9lWPC0QzA1NHFg5YYpsWr05IN1umNUt68FRp+sr6eT9jhCKL5iElQTQaN/dCrQ3PhI
        MPeuOUeTZKrN5CNmao99MH/98S8fWrvSD/jhUQuglZVI1nm8sQZPHQtN0dfzXx4cpgMqwTiYAx1f
        3duaIAlHkEcfU27jCPFSyDP111/bUa26iet+D7UyX7Cc3wvOjgT5gKl0wZXomBYfLnIMydiemRm9
        go4XBs/QrtFkOpsjoNYu6xB9vMRi68NW5n0er2MIosxljhsxxHns9GoYKRadBXqquVmvJPj2A7Fw
        LAefJ9kLP15gtl3frae8/ETo+38pLB5NPvPFYg+5f6Bs0w06n8/W5gJpMa+JqyV1x6O6jNE+NhLm
        WIpqzdUxiUG51y5dnKU1HzFxWoRUOSH+tx7nR6s8IdgLRzzdUfv1NxVDmIQp8apbx6cyUjB88y7z
        5rfRiZ9Fbv7yA+W/fkXbq4hKXCyY/vPHgFr4l/eJq35uvP/lZ/1s63T88vrIukGGw1VbMKO2t/XY
        xMIFfvpsvN454tL1foHb+5qzwF/p6Ju3XNBhJriThSEYyzpoAaSlzjZOOdXdQhlFYO14YrfrsuCT
        N9UuzPZ6Q9xT9UBcxPJ3XhD25Ku33XJ7vwI0ZNaJw9gz+F0PWr2OZywOvmiN70BqYJs9pS+PVgn1
        ps8TGXIPxDiVQ8BELJewslofi3rY1Uy1I6oVwvQiRjk/u8/B2B5/PE5yPdcsZu6ooPD29cTPWAm/
        eV+pQHaHhGHpRHIOidfCl2+Z+ejWQZ+Uax2qQykR86rYSDIby//55c8/0Vz2tgNeeonxEtVdPS3G
        MQViiwZxzaLPmRyJIVwfbkTn6sotrtRrG1T94zHHHKWOohulQFuik29+7ebLaZjhfVI9+prVjo/c
        IyJqZedC1plX13PmgqQuT5sWS9FTyKd08iQl0z+IWHsk8vlJkky5XlZHZtn8GbBd34l/rz8oFqQb
        9485g3O9IiS8ydSiU9i0igrv3XZU10XQO1cnhfOu+e6PI97JfZaqCTg+Xubxy+LbdFsqy9O6xbfh
        fMv/8tllThrcHjsnnw/+Zg+pJjssGd+dNRweL+eXJ/DYOTEaBacS4eAVWyxc5cAassU8gp3jhrgD
        syzu39YZbE6pzr58FXA9tI5wq5UtnuEydl/+NZFnFhQL4trNZzNbSmiK6ZaR0aqD2TsGmfrZrnbE
        1cN1MojQUvVNHoTQRZxYUxODim4QOuQS7QfEhssYI08PzsTg066bfvn+L5/rI6/Zz99//R2+nNL6
        8TcE0iFiDqe3gH7qiYLeHRW2XdtSR4fHMoMFO4bM+OaH/rK+z9Cj/EW2n3785oWgQbtzUxG7a1U+
        OcazBdn21swvrk43L9pFr3bRUf3qfcPH4WlGYHRLEZfLO+Hcn44RKLcIMzMypfzTfwoVfedXVNvm
        YfDtl6fq77MFC2b10c0d1lVoUvlKzMg8JmN3Oh9BlW2Lff0i4YfL8Q3nekmwHJs94vtLmWq/fprm
        N7WGztZc5bc+eKlJMi9SKqFimw4kpLKeSxZ4Kazjl8UCp+wSxpxlD73iUmbiQ5P3umUXWmrIAgkX
        OrHmX72/aHggTrw/WbOIVRuMzxNR5JRBPgvOFdAyvl/wyl9tginLd6b2ux+2XRvBcptmKaSa6rAw
        Sw/B6C7Dt+Lp3vk778PB+6iNrVYvQ8z8RXxO2KVUKjXWJPebj0prVNF5RmyrEPLN66jzprcOiiIa
        xDJ04a++qriNMLmZ5SPoH04sgAVOzZz3fZ8zU8oK1WuHgLl6+EimT40idDj5MjE20NfzllilcjJS
        i+7Dg5Hw73wEYvrp/p0/NtXYAk/GGyHPUEyGo/iZ4fVpN1T0umvH9HFw0I8vouAMNSdBa6IrjXtC
        zHId8CmsHTiuMxWfSH7IeSkkKfryM7Hxy02+PNbCt/6pKkGLmBtdLmiOBIKF+gFo7G2hB1MCAWt3
        NcrF/rR5wrMi228e8TuuvtULKgg12fra0IQXgUvRV99YcnEXaHwVZwz3SXwRw+GfYPrOd5Cdhw1J
        u/Ut/85XY4jrrcocsfUDNBixrUm4avDi/naCeZumImih2DBvVN98FNdKC6rHHxTyu1xTc6dGQJ1y
        YmFv2MFy4dwqdBjcC1nX9ivpB+NdgRzob+KtbUAD5lREc+Z0xF/yV843588IX71i+JXaweiwU/x3
        3vU9nrV8F1sZbYzsQpWXlQWTfkhmUISM4La5Y2tykkWJeq0HjHAc1Oz1Sp3fPJKisJn5XL17X/XN
        xQfzakuDHpLIQVNI33RqpkXNz4diRJfisac/PhsX91UM86G6Yu1xQMH0LrYq0izq0tkMac1XgSOh
        K9JOLEBLLxnLPgJo6iKi6OtX/WdXC5DOlwMWVuWMGumJCng2VcVItvBynrkoRtMpytnV17/z4mel
        K96qSdh+YVT19OMHLux3xE2dfcLLzcmEV4d0vCy3QdBv215CCjgqs0J9Z4mH5DbKc4k4/fqBRY0h
        uvzlJX/JNzkVtHivbj6twowdflojvt8q2N/mM9tmwzofHX9ZoYMUK8QhhdHRGJUOxOPaJMYRnfh4
        uDwctSC9yRzmR4g/yFmEZVxfiOfgV94Wz/KiLfNOxmos36wp81xX/fIe0xsxQOO1FELI5XdIsnsC
        1rzaX3uIC7qhstet8mEjRClcHZN89TupV5tNpMKw3OQsYJsimbcwP0EOzDdbL1QjUIF+Mvi+/yFB
        OrzrUZ3DAu5StiT+w7Hyv/NaNRVMuvz679iVexUiplLmCaEZiOXmUYA39iNxzPCd02C1xqBVksdI
        1w85bygO4XOLX3gpHs5omr3UBFxnHfOp8bKaNbub6Jsv2enxjuvZWNz3f+cxG7RkHWPdB8Nznd2J
        rocTmmu9LdQ+8XziOksDSd4tpeozmlYEM7/JOVF2tnZ4N5iFnpNzziLL1NZRQsl3Pt193ydlcAqn
        9C8P0nirmFD5C4t5xrpGbCqQAJE+U+ZIacVH75jGKszRTHQZspoPcthDSl5vLAm+ZVHniSX0PT+z
        z0Swvtd3QRu/lInubZZdv2IrE/bwWuM6bBb56Dxz+m8/tMt3Pgup3CCBjku2O+vPZCZB94Rv3mX4
        JFr1VNmBgLBKZroA7cbZpZwq2Mm0ZmvxcOZffX1CJz9OjKRDlc/9duWrYqzfmb9jqO43vWf/9b9T
        qPuIgy7PEA2vM00uw8Gii0P0Bn9Tr4i3zefvPP2tImRdzyyUWr1bjd3tAhNdV8xu76t8ZCIPoW4f
        C2KHh3vCJDOIwYguNwxetwvmT9ZnMIz1lgTxxwjG2JYb+OrXl6+Hbt7LrgvhMPrEj/cj7+TMK+Hy
        lDHxU21bjy8T6fCd91DNmVn+5YM3wMc/MMNNqmS2PmMFoPU1M/fnjs9xaaia9OQmsY6foe6bizXK
        0DQblj40E/Wr4BqpFf3EZHuYsq4T70aqlvz508Mm6L1bIqqWwjFG1fZe00JWjpAsw9ffvDNLiocB
        vwQFv8n4qOddP2RwUbwNM/R7FUw//3LC/wEAAP//7NrNkqIwEAfwd9krWwWCknB0AJHvj0GEvY0K
        iggokgRTNe++FcYn2Kq95QG4dFHd6X//Yhkt2b7D7hcu0Fb3OFxVMjYmqbAhfL0yJ/qY71/C4Ddw
        iZCCRlmvjKEtsgJ6H8EQiupQDMTt86/3/REsDONn/rF+ixLWb56s3wBRKIPIl2BHMZVfvtb5RAzF
        ixGDKUk+a00qFj2Osoqk0614fAJ1E+TYgNMZ9PTwKKGd9Xq4Z/cbJEgbHd7a+Ix98dakY0BJA3en
        k4mAMJSAWm5nqXO+0UhQSknydSngbXQNvJkq80DuySDAP2tnjU27Vg3C5oHG9jccxu0Exi5zd6C3
        Sjrnw3R8Akzm/CKcfIW88+pC0a/YH93tFRf+4Z2fhp/LJcX9Kvfn/QsJLQ3BE2sygdK+xQhs7yEl
        Tn93IelFbX7vHqbjw4HwGDgC1qmbe72CRV3dLdcKtrL67pGXYveA7WeRF4zyYZL1wta6cxWh3FU6
        j5a4PoL5PbiXYWIobJ6tErG1IktUJDAk17IGEc7y+X8daLOubLWLzB3S2rIcnhA1NZzr9YrKjpLL
        vnehY45dtI3zdboYQve4mvdB80vb0ykRnRbosiBgj5aUzvdBIGfZE5ud5Q8Tqx8E8f6CnUfie4rn
        7FoYCOkmXHpaeSCFf3nAh79J2f38BEahUG2YVpGO1PpYXVl9EbzpbYK3vdxcp+HVvPNDl+WlP9//
        mlXA9+9/EAULLgq4KOCigIsCLgq4KOCigIsCLgq4KOCigIsCLgq4KOCigIuC/ywK/gIAAP//7NxB
        CoAgFIThvaeQtxfEpXeJEHy0KFPyBW26e5gR3aDNbGczF/j4IQogCiAKIAogCiAKIAogCiAKIAog
        CiAKIAogCiAK/hYFSuvhbhakHHlpMED4EPNSARNiMNa6HjbYa5iY/CMQqGw5FRklz7xW8tp1akCS
        JSyfWbWjU10AAAD//wMAimBSZTBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c08c68254e-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=****************************************.1g-**********-*******-KGXYZS0rOEcsMBKpoVwamhRa.2117ncdV9N2mUOwfAucYYbiB74xQWsxN97CMz.YFBjGmoL_CMFYB6Gm4_Qrgg;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=rv8w90yMYXGI.ly_TuaNH01WH5YU_8oFbQ81fuwOfFk-**********365-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '23'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9996'
      x-ratelimit-remaining-tokens:
      - '9999912'
      x-ratelimit-reset-requests:
      - 18ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_be77e3e33ef16e8dd0cac4a4bfc6f601
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [9642]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZSxOqPLet+/tXrHq7nCoRgYSvx03kmiCI4mmBIgIickmA7D9/StdbZ9fuWKVG
        wTjnHM8Y+e//+vPnny6vi9v0z3/+/POqxumf//N97Z5N2T//+fN//+vPnz9//vv3+L9WFm1e3O/V
        u/wt/71Zve/F8s9//vD//5X/WfSfP/9II5TILX+mwzyJsQ23Yq3SoPF3wzKJ1waGjuRS99Uvw3or
        mQmvwban5l1/xbMmlKJCnURDi3Pbg9lgKgdPlCpo1X1U81f/IQK4RitGm4h5i/xYdSWMkIm1JHh4
        S7nNOBinyYNae11h5J2os3LB1kSt3bONmSrJqfzh0g7rQup485mvz/B1cB9EesnneqnON15O7psN
        4ZxrC8ZT/KmAKZ9faNWXms2N+mrgRkU+mj3uCNazJbnQjIwRCanC2LI/ORHwNveR7stgMJhA5RMU
        X22O8Zr0Bj1mESfPdFtSdWxHY0wLuQQ2vxpo9zTNeJ60soKrhEasPqQ3m+cQj4DToIvveGLefLH4
        CHLbl0Kk81kwCDdoN+h2SkDkpNIZX7R1Ax2TvgncH3RG33PcwXlq30QRD8GwXIs3guBaKFRDr0/e
        DXv7BiIrR0jJpn3MHLluQUEEg0gHcDXWNOlNeCfhiP3UIt6MvLkQnYMOCVjlVz3rDQrB63pNsXEs
        e4PYA+9DraIp1ZSujOfFXyogn7sJLUNef/djPUH2QCFh+8spnw/clYPBy/5QD2/u8SBQ+QypTAHW
        q3vsLXrjEzi9sxkHe/Ncs+f1LSuBdsvIcrhc8nnjpz6oBOij5Hy+5YvnPAvwOtgPfO+PLza/E0+E
        r/umJfPkPeOlOB1tKAbxk+qbHfCIPlkEfv8PtDuWBttZ4tmG0NVP2HqnYT0f64aDenB0sSNtx2Fx
        ZTLCzzbyqbnR9EHYu2sKBt5wcKCjpzdnjkag7WmMDGdzYCR5TSUsP/qKjvlOryfuqYyyELICSWv3
        HFi5tUw4FJZKpLhUa7a9QBMoA3xho1qNfHbkVQWFzT/wni1D3u01ift7PeQNE5gtV6kg51ktRsZJ
        jNdml5hAl8IYcTa/DJPIiTZ8PwwR+5DY3i7+eCK8+CxFYkVDxsrDQwf7yd5idBcHb+104sKjEi7U
        Hnq+Xg9bp4RFkE1Yv3mjN+tT3IDXk0Q4GLJPvkTcjocvpOtUL2kWz6kcjjCsvA77vmR4y6tn7kYV
        kUZx+ahqpr4/FViVVcY+0q16e6zrFHBP08MW0aZ82Xk5AVs1rKmTTUNN6MFRJbW5n6nqhpI3tOk9
        A+nJ2VG8Jq63bGYxk6M4PKOTu7W95cXbN/jWEkTt7dPI50nMbZjlkYNWzfCMmVOSAsKPnaBXsiJv
        aXbnHq4xtjFSmiweNaGUobBPbZxWNASkH5UIsFUscUpEq56//QoPA0QIts8eTMctDMF1G74onq6b
        nNKr1UD/srZYax9pPD8y1VSu9Kiirb/rc9qoFxtK+vVAndXwY5YJ/Ql864vk8WrWO+65NjL3tDyq
        8sqzXoP+SYCNuzt17yfD23JKLsPXXWmxc206g6nSmikmJzIaXB5xPA/IV4FwBTL69XsvjjH3d74G
        gv7wOjxnPsz3rMTWQ6+8ueQ+AoyPZYrRha+HtdNbF+JaaqmnJNUwQVfz4XCSbIrRkcvXb73LwVxv
        SAJqb2DwlVVwp522+LQfzXpNNG+FR95OkLQaY7zW5FqBI1Yptep2jnseyRbIOC4leTAJ8XgYNRNe
        7/oBSQZUDeFTLyO8BrueetzAsTkuAxVKAS9Si7R6TR+HboU97HuivJLcY8C0OPBb7/tSbczVYxhh
        BgeDlGPrewze9gj2rEQI0AOX04ZYCI7axFPbU7iY0Stq5I1z6ugV1K98FvTjCK/6VUKror0ANde1
        AnmYxJMolbeYZDbPw55VCLs3GrCXEzipfDqdu9/35eRz7AQwNzlF/KXyPdrk2zPM5MylnlTrucA9
        eRe4S3ig6BknYNGFewu0y0lHbJjexhR9whlKz6eNZueQDH/7rSGyRoPnWfe+9WRB/OwnjE9nn81X
        2eJgsLtWVA98FRBOyc7gqcUh6vneAGM21cWvHn/vs93TVAVlIYeKwNjrjUU4PXVg1EuPrfvpE8/y
        R5VhZzoF1cK9PoyZ44xw4oIddtXiE6/H7TrKn5oX8OWwbxl9PasRfqwNwTqlYzzcPoopD4Wp4r37
        qo11nUQEttd0g57tQ8wJWDYtOIaFhbFUbY2xaIcGfvcDJx6x6uVyNVpYPuQEH+goAbpRsCo7aRH9
        9Coe23QQ4PNTT3hfrmXMsFfpUAiXgtr5swBjwFYbtqhSfv8fWy/KMMvz4W1gPHF8PXVzsYJsCSLC
        D70M1sYhKsj3S4mmJPDzNV3UUOHgo8S26KrGzg5vBQibQiG/+mFYP43gJuxWtGe+YKxDeZ3B937Q
        Dl45g8jn2oYKhwtsCv3kTZN29wGl5xd2DpeezZxyL0D3VFvsLYtuMHB+8PDZKA+0euOhnrRcc0H3
        ankk26EzMNd4I7izUYr1LX7VJCodGYpWkSGOOfeYsM3uBK3D2OGDQ4x6pTw5QeWdXrDO93O+rlGk
        w1JdE+yUgQkmvj/ykLbIwf69PXjsRJboN//InA6CNx2qFQHVsG4Yo2ORTzIIEYxXKaLqQzowKtD1
        DKwh21Gkz723LnI+wvWWqRgv8wmsyU7Ifv1M/XdP4pHl1Q30kSViM54HNjbq1EBp5CTq7xACPz6A
        qu+L+GC/SmMuRFhA8+k7GOneiU2fmoUg6VqEv/vt0dezH8EhDWpsdyzNl/lhC/DIuwm1N69+WJlj
        Eoj1asQ/vmRMWBEYEKiRYgWZMdw+W1OuXMWgxZZpBkvi2ITN6cioq+mXgQiS5su/eSwzkoJ1SSSo
        PCbjg53L41rP8scWYaAVGRHxvB/WwtMj2F4+GZEaX2HjveQQvIVijziYC4w0LxNBsY4tbKr+MNBg
        Dzs4N1eKSMsI+Omv7Hhvl+73+95j73dqwpo79diTxxdbz3znQ84MDKxzZFfPH7tMoYgbAds8JB67
        l3QFgsOdsU9vpJ722pzCKtub1IK3Pl6Fe63Kt9spRzwRfTYXp2gEQ3iScSCOOmNqcfJhoXGQqjvc
        1XMmiOvvOdmwYqopm0ZRrm/gRPj3oLFZtxcBHgt7RuCzeYKxvHJn+f0+rdS2KwaYOGYZVMqSp6jF
        ar1MblUpq+SPiJ3AibEALjp8FvYF+ztmetvBVFx4eszXLx/ow85y3yborBvDvhW2+TzFUgaf4uhi
        T7xeGBN0L4Tba7bBwVnmvJE5Bx5KaTZg/Sne2TqYrFWIVS1E3qLVI6lsCJBbNIpVP3nXbNdOOjgW
        7oxkI9nXMzDvKTQjbcSHZeniRZ0l7lfPdP/V63F/cnk4GPcVKf3xBZaSO5fgrV0Qdh4C9chsbSKw
        D4+EtMtN/PZ74gMDmjXazZ3JmGOIHIxHaUI1XEpGsqk8A5WCA3qFH7cmv37dlYXwtz9YeZU64JsG
        QOxmP9j046GfvljvdK5ZGY5n0N+rD9mAbe2xnYd4kF+QQDVMRsBqtRQhEI4fqttbL17hbuTkURWP
        Xx5Xc54Okwyo4ExU/aSy8f28ANH209Af3zKeRSHYcLcALRaKwYpPs/yXLwpH2Ro0i9R/57cx2Ee2
        3C/0/OMNIsNb4zEmyEjaqyeX8J+UgeVQyT5oOrXA+iW0jR2bGlGZWeN/+UP35sNV9GF46I70bEiZ
        8fUDEL5OWxeJWtKBEb46DsryGSIg1Xq8a/LtCY5r7JFJR5ox3y5yD3FXhVhNwiEn12LqoR8Cg0i7
        mfOWG9tX8ID3Fj0UmwSw3elmg2//UMQRE8xinZfwWy/Y9K7nmG3wg4Oehges0nuXrzvd5KGtgQtq
        N3FbTxG3ESB/OmaIc5TEWxoSW3Bn+yk2atCyybg8Eaj79waBTzbWy0V5naHPvyPsVQsGs3u2KqjY
        0Rkj5txzBhZBl5vFBVRXXidv/erjXz40jqXrseRVc1ApKx7v8+hgTI48F4qk7te/PDgtCSihlugT
        md9DZyww9mcozi4izEQhYCWXZ/Kvv4JZroaFqe4Ia2m9ITF/FoyeMXAhIsINVbylG2y6iRGM5/5K
        9fDtDazQWAaOjSKSVZ8h6M2y9sHHiQ26TwKRjXm0j6AXZja17JACxiJrlP1QMsjKkUvN9HonwG8/
        YANFovdp8Yn78QI1zfpptOL2E4Lv7yVw82rylW02J5i7CaGHYVLZejUON5gW6x7bSlwPLKzLCJwi
        LaaWIcnGWp3jCErP2iabq7BnM8JWD4Asxtj91uP66qUWeifujJYn6L/6JiPox36KneoxsKUMJQS/
        fpc6a6cN/GeT6z//QNivX0Fw50GJig1Vf/roEQP9/D625c+DjT//rF5NlcxfXp/pMIkwuSsbqtVm
        UM9NxN3gbz5r7y4HTLg/b/DR3XPquTsVfP2WDVW4YjSI3OTNZe31EApblR6scqmHjTTzkPbzhT7u
        24ItzlLbcDX3B2xfqhdgPBK/eYE/4u+8HbbB8w5hg1cVW5S23u9+wO59viJ+cnlj7jyhgUHWCl8e
        rWLiLJ8WaOIIsXYpJ4/ySCzhzuhdxKv+UFPZDIlScMsba+XaDp9EC84/Hse5misG1Y+Ek1j/blEb
        Sf7X70sVFO0ppki44JzB2Onhl2+p/hr23hiXexVWSSlg/S6ZQNAbw/3p5U8/wVqOpgWd9BahLaiH
        etnMcwqxyWvY1osxp2LI+/D+skOyVndmMKnem1BWPw619FkYCHgQAkmPVfz1r8N6u0wr7C6yQ96r
        PLCZOZgHvWjd8D5z6nrNbCjI28uhR0LYcvmSLo4gZeoHYOMEeLa2OM6k+213pobJWo8ex4H/e/9e
        scHDfHqtGbzWO4z9h0gMsvhNL8mwOwazvC+80bpbKbwem+/nUcgGccxSOYaWi7Z59DZYkAaltL3s
        e/SYro/8L5/d1rhB/Xmw8jVxDyeYKqJF47kbjCl5va2fn0DzYEVg5qyKh4lTBIi7i54xZZt1hmaO
        GmxP1DCY+9hn8HBJVfrlK4+pvnGGj1oK0Apv8/DlXx04ekEQx+/tfNWzrQCWiAQUz0btrc7Zy+RP
        sDtiW/X38cTDnsgdfmFMNlFsLE0EZfCAvoVv4WkCdLrNEXBU74o1thyH5efv//K5OrOa/vT919/+
        2yqNH39DT0hCajHy8MinXghUh7NEg70pDGR6bTO4oWefal//MN72zxWOIH/j4DPOX7/gNeB4bSps
        Dr3MFktreyiazp66xd0a1k2/GeUhPMvfed+weWr1EGrDlkfl9okZc5dzCKVHiKge6kL+GT+FDL75
        FVGC3Pe+/dLK7inbUG+VX8M6IFWGTSresR7q53geLtczlEXToF+9iFlyO3fwWm8xEiN9BOx0K1Pl
        10/L2hFjGkzFln7rvbccx+smJQIognTCPhHVXDCgk8J99DaoZ5VDTKm1HeEo2YTqKGnyUTXMQkk1
        kcP+RsXG+qv3N/ETbEWni7HySDah9mkBAVbp5Stn3SHYRs8b2rm7g7dk+VFXfvthmrXmbYM0S2Gq
        yBb1szTxZnvrd5KjOtdv3oe87qzMvVJvfUTdTXSN6a2UKjlSBPvrj0pjlsF1BTSQMP76dTA4S6dC
        SeI1bGgq93e+yqgPEX7o5csbX1bEQQNaNbW65ymnupAVstNPHrVV/xUvnxqEILm4ItYOcKzXABul
        dNFSg5z8RIvZNx+BEfkM//qPQzX3kMXzA+PW5+PpzH9W+P70B8I7w32g6jxZ4McXoXeFNcNer4M7
        iUaM9XLvscWvLXjeZzK64DzJWcnFKfjyMzbR246/PNbDb/0TWYA9oHZ4u4E15DDi6hcE82hyI9QF
        yCHlKYc5P14OLWwrHHz9iDswuZNvoMBEp/t7Q2JWeDYB3/lG45u9AfO7uCL4XPg31iz28ZZvvgPM
        3G9wOuwf+TdfjWBUBzK1+N71wKRFpiKgqkGbZ2d5a5CmPFR8vqHOLHds5vdSD2WHvQjMn2JN9KMc
        QmKVC/VHzfS2G+tRgWSyb3hfm+94nLSugqKndtjZmxBMiBEerJk1YHfL3jk7XD8z/M4rit6p6c0W
        vUR/867v9xnbrghEcNCyG5HeRuYtahKvUOIyjPrmiYzFijclGJURIoAir6bvd2r98kgC/GZla9WN
        ruzqmw9iVUC8EcahBRafdGRplk3Nrkkxg1vxOpEfn82b5y6Ca1LdkfJKgLd0RSADxSA2WXWf1Gzn
        WQK4A+VCPbB14rkcQwibuggJ+OrV+DnWHEzXW4K4XbmCRmhBAdumqijONk7OMhtEYLmEOb276jcv
        bitVcnZNTE8braqXHz8w7nTEdmqdYlYeLjp8D0BF2zLwvDHoRwFI0JKp4atHg0/ixyyuJWDkqwcG
        0abw9peX3C075IRTopN8+PQS1Y6oNWb0fFTw9FivNMimfT5b7rYCiRBJ2MKFNpAIlBaM5r2OtTO4
        sDm5vSy5wKNOLeqGgL3wlYfbqL5hx0LvvC/a8qZs80FEciQ+jCVzbFv+8h5VG94D873kfJiLnY+z
        ZwyNdXe6jzAqyIGIzrDLpwMXpvBu6fg7v+N6dziEMpy2h5x69FDEawDXFoqe3tH9RtY8GZJPBr/n
        P9hLp66e5dUv4FPItth9WUb+N6+VU04n26/+zkN5kmFIZUIdztc9vjy8CujM44wt3e9y4u32CCqV
        4FA8jFPOGoJ8+HlEb7TlkytYVifVIaqzgbpEexvNnj518PWX9PLqonrVNs/T3zzmALZ0oHT4INju
        sydWVX8Ba632hTzGjotta6sBwXmkRG7DZYcRdZucYeloKknXIOo7Vs4YDQ1d2Ycxwd98evieJ2Xw
        4i/pXx4kUSDpsHI3BnW0fQ3oUgAOhupKqCWkFZudcxrJcA1XrIowq9kk+iNM8btDAucaBrFaJIDv
        9al5xZzxvb8bOLiliFXnsB3GHd3p8ATfe1T7zSafrTYn/+qhWXb5yqViAzgyb+nxqrbxir2hhV+/
        S9GFN+qlMj0OIBmvZAOVB6O3cqngUSQ13fPJlX3nawsH8XWhOJ2qfB2DnSvzkfqk7pGCejyMjvlX
        /y6+6gIGVXGF4fS+kvg2JQbZJGEH3UO9w06Qr988vZMBMO5X6gu9Ouzm4XGDC9lX1Oyfu3ymPPNh
        3b822PSTZ0wF3YugFt4eCDrD0Vs/2ZjBaa4D7EUfzZsjU2zgd359+Xoa1pNo29CfZhe70Wlmg5g5
        Jby1IsJuqgT1/NaBCr95D1GsleZfPugg/LgJ1ey4ilfjM1cQKmNN9dN1YGtUarIitEzHxvkz1WNz
        M2YRNs2Bpi9FB+POu4dyRT4RDpIlGwb+qaVyydrfPGy80XnEvGxIDCFQBc+aFKJ0hvHWf//1O6sg
        OQiiNyehDs+vej2OUwZvknOgmvqsvOWnX5YfCkT8+p3v+YULFOkTIukhUGPhUxvCdU0crP3Ov7jB
        b6BIyI5Mgv4whjZNUuhpwYA28pAOs9tdsn/PH8HWMP7q33fekuN33ozfeQM2XBFgn4dvRpmw+srb
        nzdo8zRCsByPUaXw6bajOHnM8fJK+wjI++BCDbiUoGN5X0A76XR0/p7fEI7f6/DVhiX1N68mngI2
        N/B0v5sEcEMBmOW+LfmXbzQ85OP5mD1T+Jpcg+6Xh5nPn+PAwavqqNS0K9mYv3qgfP0bRWG7gOmd
        uCfQWQX75cNsGgGdf/kFWvzd/G9ene70mvqTe6hp6uf/5qcoEkVGO+ni//wX4VqGwEgVYYb8uaUE
        HD6IzU73ceHcbZQf7+bLrXcgvAUOR3XmXrxuRze6fBLVHbWS6uPN687uwNefYS+YhHwR9NRW3uUD
        k4u7e3usoNUN/HjwLMCjsfvqmXTctBa2NjseDMe6qACmyeVXrwNr1Ictv7F5IkpbFMMISVPB336t
        uHiz+XnuXOiY0xsfwosabwfk3qSfHzQz5cyW48ZpgS5wHPVYwdjvfBAISTLS/wcAAP//cs1z9y2q
        AIWfhXlgeEaZV2GQr4+xj1doroWfdoibv4mPZWpSeaRvRqFFoa9bCGj+PMW8RDvSzNMiJC3AudQs
        MzktCxS+pRY5zrlBZR75RtlZFUWV2dDxQ2/QeClEvxJ4VUCtDhkrCgxHVxSMrigYXVEwuqJgdEXB
        6IqC0RUFoysKRlcUjK4oGF1RMLqiYHRFweiKgtEVBRSsKAAAAAD//+zcQQqAIBSE4b2nkLcXxKV3
        iRB8tChT8gVtunuYEV0hmO1s5gIfP0QBRAFEAUQBRAFEAUQBRAFEAUQBRAFEAUQBRAFEAUTBH0SB
        0nq4mwUpR14aDBA+xLxUwIQYjLWuhw32GiYm/wgEKltORUbJM6+VvHadGpBkCctnVu3oVBcAAAD/
        /wMAimBSZTBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c09c1415a4-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=.BDNmN1yTewmv2k5bcIIWnuaGOLSo_VQuMVdbkfADXQ-**********-*******-u6aSK1zNI7dIiBslNgn6x3dcZ5NnrknbDjvI3zbb9o.HZbNSjpgDnOtuO6L0w2LHXNYz42N.frX6vMKRqRmpVA;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=T4sDDTq3o1.X3WdyXU3mpzCYUwWgpTvhpokK_lU3iRk-**********367-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '19'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9992'
      x-ratelimit-remaining-tokens:
      - '9999517'
      x-ratelimit-reset-requests:
      - 44ms
      x-ratelimit-reset-tokens:
      - 2ms
      x-request-id:
      - req_ce4e43fe6b01063fcfa60447897ba9ca
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [2822]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkka
        Ye+1vrX57//48+efd14Xl+Gff/3551H1wz//+f3tmg3ZP//681//8efPnz///fv8fyuLZ15cr9Wr
        /C3/baxe12L6519/xP/95f8W/evPP0oPCr3k97QbBzlxYSnXOts24aqbBvncQOQpPvMf7dTNl5Lb
        cN4uW2ZfzUcyGlIpa8w7GHjyLms0WlwXYM+YhmczxLV4Dm8ygjmeCV7EPJjU22xqUYxtYhy2t2Aq
        l5kASXq4MWdtapy+DvqonYgzMGd1fyZcV9RU/Qjpm5hS6gXjUayP8Nj4N6o81GM9VceLqB6uiwUV
        vPMT9fvkUyFbPT7wbE41Hxv90cBCxyEeA2GH5qOj+GDHVo+lVON8Wu+9GAWLa8/W5bazuMTUPciP
        Z07IfGgttstiQR3ZsmR6/+ytPi3UErnibOHV3baTcTDKCmYF90S/KS8+jhHpkWCAT65k4MF4csQY
        hOVDo8rxKFlU6IwL+G9tS9VDZXKxeNYNeDZ7UVhvTM5eY/KGcXi+qCZvtt10Ll4Y0LnQmIEfn/zd
        rd0Lip0cYy0b1gn31PqJCipZVNmgszWnh9aGK416EqYODUYcjIXsbUygaFYf9Wg2OEKP8zkl1q5s
        Lep2YghGxVJmaO8yGadwqpB6fA946vL6ez/mPfAbjihfn/b5uBHOAmwf7ocFZHFNOompR2AqQ8Ss
        rkkwmU1IYXhlI9mu7WPN7+eXqm2NS0anzemUj4swDVElQYgPx+MlnwLvXqDHxr2Ra7t78PF1CGR4
        XBdPOg7BPZmK/c4FeZvcmblYoYCag0Ph+zzwaldafOXIRxfAN/fEeaVRPe7qRgBzu/OJpyz7bvJV
        2sNnGYfMXhhmJ639OUWdaHlka+J7MGaeQcENDE67o91xengMJZQfc8a7fGXWg3DXelWKeIGV+X3v
        eLl0bOgKR6dKUuo1X57ARloHD2JVs5WPnjrrqHDFG1nzqcvfa0MR/p4PB92ARsfXKhAC50mwtZeT
        uVkdbGQqUYIFV5y6QRZkF143SyYhUDdYJZ9AhlPIUyxXLOK83NxMtB7cJcFXuQvmt0l92GnRxNyu
        Fet5s/RKKLbZQMxL0AejOSQNetxpTLZd9smnWFiJ8MCmycySZcmYqlEPURW8SRgqVjA9Wu4vdBkb
        jJS3qub661OhWZtVEmLTqZe7uk6RcLcD4lBjyKdVkFO01KOaednQ1ZRtPF3Rm+uR6X6kBN0zvWYo
        3XsrRuaDH0yLUc7UOImOeO8v3WB6iO4FXsYBM3d5t/JxkHMXsjz28GxYgTUK2qEA+LgH/DjMOJia
        1bGFOSEuwVqTJb0hlSpI69QlacUiRNteixGf5ZKkVHbq8duvsOkAY3jeWzTslhCh8zJ6MDKcFzlj
        Z6eB8DQ/ifG8pcl4y3RbO7Odjpfhqs1Zo59cUMzzhnmzFSY8k9o9+tYXzZPZrlfCfW5U4e4ETBe1
        ez1v2ztFLnlfmX/dW8FS0HIVHlftSbxz87a4rsyZZgsyZ9vTLUnGDoc6ks5Ixb9+b+U+Ef7q61Yy
        b8GbjFkI+ZqXxLmZVTCWwkeCZFemBJ/Eupvf5tMHUitPFmiHqhvAN0Lo9orLCN4J+fytd3U71gt6
        QHXQcXhkFayM/ZLs171dzwcjmGEnugeszFafzDU9V2hHdMac+jkmrYhVB2WCkNJ8O0hJv+kNG85X
        c4MVC3RL+tRTD+ftqmWB0Al8TMqtDspWlJlDn2bNbpv3DC20LdUehzzgyHYE9FsfhkptjdWt6yGD
        zqJl/wwDDpc1hpaXGCO2EXLWUAdDbwwicwNNSDg740ZdePs3O6P6kY+SuevhbJ4VPGvGAzF7niuU
        R4dkkJXyktDMFUVoeYWJf2Fb/vC2Xqru98f373g5/ezeEhqbnGHxVIUBa/LlETI181mg1GYuCXfR
        R/4UbRi+Jwc0mdL1iYzT3sS8G17WEH+iEZT73cWjtzl0f/utoarBtvejGXzryQFybwdC9seQj2fV
        EWC7OlfM3IY6ooKWHdHdSCLciq2F+myoi189/rbz1d3WJW2im4pCErTWJO3vJrLqqSXOdf9JRvWj
        q/C2vYIZ0drs+szzehiE7Yr4evFJ5t1y7tVPLUrktFk/OXvcqx4+zoISk7E+6S4fzVa7wtbJ2n/U
        1jwPMkbLc7rA9+dNzimaFk+0iwqHEKVaWn3x7Br43g9yCKhTT6ez9YTyph7IhvUKYguN6KqXFvHP
        r5L+mXYS3D/1QNblXCacBJUJUjQVzM3vBeq3fHbhiSvt9/z4fNK6UR03L4uQQRDr4T0WM8qmbUzF
        rlXR3HhUR/l6KvFw2Ib5nE56pAlwK4kr+7q1cqNLgaKm0Oivfjgx9z26SKsZr3koWXNXnkf0vR68
        grNgUfVYu6AJpCC21A7BMBjXEDF2fBBvc2r5KGjXAr3v+pME02RaHB1vItwb7YbnoN/Ug5EbPno/
        niJW3cjruG+9MKxcnBJzSR41jUtPBdkpMixw75pQvljtwdn0b7LxqFXPTKR70F7piZhiO+bzHMcm
        lPp8IF65tdEgtjsR2BN7JLw+NwHf0yn+6R8d004Khk01Y6RbzoUQvCvyQUURhmRWYqbflA1nEpuP
        yOmyFcPm2AbzpOY9zJdMJ2Qa92g+rKTs188sfLU06XleXVAbOzKxk7HjfaMPDSi9oLBwhTH68QHo
        YSiTjfsorbGQoQD7HnoEm8GeD5+aR+jwfmLyvd8Be9zbHm3SbU3cN0/zaby5EuxE/8DcxaPtZu7Z
        FIhZ9eTHl5xLM0YdRjXWnG1mdZfP0lYrX7NYseSGxQ9JYkOz33HmG+apo5JihOpPj1VOUzRPBwW0
        22B9iHe6netR/bgybI0iozIZ191cBGYMz9Mno0oTary/lgKGSyS3WIBc4rR52BjkOnGIrYddx7Zr
        eMPYnBmmT07Rz39VL3j5bL1etwF/vVIbamHfkkDtH3w+iu8QBHtrEVOgq3r8uGUKMmkk4opAA34t
        2YwkTziSkF1oPayNMYUqW9vMgUubzNK11tXLZZ9jkcohH4t93KMu2qtkK/cm53qxD6EwBGD6irzr
        MZPk+fedLngx1IwPvazWF7Sn4qsz+Gi6kwS7wh0x+izuqC/PwlF9vfYzc92KIy73WQZaWYoMP4le
        T4NfVdqshD3me7TnfAuTCffCPZFwxe1g2dmaD/vbeP7ygdmtHP9lo7dz4SR0omc+DomSwV3ufRLI
        5xPnkhlEsDxnC7I9qkLQc28jgpJmHTHv8pXPnc2fGnWqiapLPAc0VS0JhMlgRA8Pr5qvnoOJdoU/
        YtU6rOsR2dcU7NjoyWaa3smkj4rwq2e2/vp1v977InTWdcZau3ugqRSOJXoZJ0y8m8QCOjqLGK2j
        HaXP6SJ/+/0QIgvsGq/Gt825Z8kCJL0y4BqmktNsKI9IZ2iDH9HHr+mvX1dlIf3tD16elTcKbQth
        fnFvfPjx0M9fnFc61ryM+iNqr9WHLtCyDvgqwCLKT1hiBqE94rVeyoCk3YeZ7jJIZlj1gtrr8u7L
        43ousm5QEZO8gemfVLW++0uAl5+G/fiWizyO0EK4bPHk4ATNZD+qf/mi8LSlxbJY/7d+W52749P1
        xI4/3qAqXJqAc0nFylrf+1T8pBxNm0oNUfPWC2KeItda8aGRtZE34Zc/zGDcnOUQos17x46Wklnf
        PADw2C99LBuHN+rh8RZAVY+AkVKbyarJl3vo5ySgg4kNa7yc1BbIu4qIfoi6nJ6LoYUwQhZVVqMQ
        TBe+rmBD1g7bFIsD4qv9xUXf/mFYoDYa5Tov4VsvxA7Ox4QvyE2AwCAd0dn1nc8r0xbBNdAJPxfJ
        sx5iYSGBuN9lWPC0QzA1NHFg5YYpsWr05IN1umNUt68FRp+sr6eT9jhCKL5iElQTQaN/dCrQ3PhI
        MPeuOUeTZKrN5CNmao99MH/98S8fWrvSD/jhUQuglZVI1nm8sQZPHQtN0dfzXx4cpgMqwTiYAx1f
        3duaIAlHkEcfU27jCPFSyDP111/bUa26iet+D7UyX7Cc3wvOjgT5gKl0wZXomBYfLnIMydiemRm9
        go4XBs/QrtFkOpsjoNYu6xB9vMRi68NW5n0er2MIosxljhsxxHns9GoYKRadBXqquVmvJPj2A7Fw
        LAefJ9kLP15gtl3frae8/ETo+38pLB5NPvPFYg+5f6Bs0w06n8/W5gJpMa+JqyV1x6O6jNE+NhLm
        WIpqzdUxiUG51y5dnKU1HzFxWoRUOSH+tx7nR6s8IdgLRzzdUfv1NxVDmIQp8apbx6cyUjB88y7z
        5rfRiZ9Fbv7yA+W/fkXbq4hKXCyY/vPHgFr4l/eJq35uvP/lZ/1s63T88vrIukGGw1VbMKO2t/XY
        xMIFfvpsvN454tL1foHb+5qzwF/p6Ju3XNBhJriThSEYyzpoAaSlzjZOOdXdQhlFYO14YrfrsuCT
        N9UuzPZ6Q9xT9UBcxPJ3XhD25Ku33XJ7vwI0ZNaJw9gz+F0PWr2OZywOvmiN70BqYJs9pS+PVgn1
        ps8TGXIPxDiVQ8BELJewslofi3rY1Uy1I6oVwvQiRjk/u8/B2B5/PE5yPdcsZu6ooPD29cTPWAm/
        eV+pQHaHhGHpRHIOidfCl2+Z+ejWQZ+Uax2qQykR86rYSDIby//55c8/0Vz2tgNeeonxEtVdPS3G
        MQViiwZxzaLPmRyJIVwfbkTn6sotrtRrG1T94zHHHKWOohulQFuik29+7ebLaZjhfVI9+prVjo/c
        IyJqZedC1plX13PmgqQuT5sWS9FTyKd08iQl0z+IWHsk8vlJkky5XlZHZtn8GbBd34l/rz8oFqQb
        9485g3O9IiS8ydSiU9i0igrv3XZU10XQO1cnhfOu+e6PI97JfZaqCTg+Xubxy+LbdFsqy9O6xbfh
        fMv/8tllThrcHjsnnw/+Zg+pJjssGd+dNRweL+eXJ/DYOTEaBacS4eAVWyxc5cAassU8gp3jhrgD
        syzu39YZbE6pzr58FXA9tI5wq5UtnuEydl/+NZFnFhQL4trNZzNbSmiK6ZaR0aqD2TsGmfrZrnbE
        1cN1MojQUvVNHoTQRZxYUxODim4QOuQS7QfEhssYI08PzsTg066bfvn+L5/rI6/Zz99//R2+nNL6
        8TcE0iFiDqe3gH7qiYLeHRW2XdtSR4fHMoMFO4bM+OaH/rK+z9Cj/EW2n3785oWgQbtzUxG7a1U+
        OcazBdn21swvrk43L9pFr3bRUf3qfcPH4WlGYHRLEZfLO+Hcn44RKLcIMzMypfzTfwoVfedXVNvm
        YfDtl6fq77MFC2b10c0d1lVoUvlKzMg8JmN3Oh9BlW2Lff0i4YfL8Q3nekmwHJs94vtLmWq/fprm
        N7WGztZc5bc+eKlJMi9SKqFimw4kpLKeSxZ4Kazjl8UCp+wSxpxlD73iUmbiQ5P3umUXWmrIAgkX
        OrHmX72/aHggTrw/WbOIVRuMzxNR5JRBPgvOFdAyvl/wyl9tginLd6b2ux+2XRvBcptmKaSa6rAw
        Sw/B6C7Dt+Lp3vk778PB+6iNrVYvQ8z8RXxO2KVUKjXWJPebj0prVNF5RmyrEPLN66jzprcOiiIa
        xDJ04a++qriNMLmZ5SPoH04sgAVOzZz3fZ8zU8oK1WuHgLl6+EimT40idDj5MjE20NfzllilcjJS
        i+7Dg5Hw73wEYvrp/p0/NtXYAk/GGyHPUEyGo/iZ4fVpN1T0umvH9HFw0I8vouAMNSdBa6IrjXtC
        zHId8CmsHTiuMxWfSH7IeSkkKfryM7Hxy02+PNbCt/6pKkGLmBtdLmiOBIKF+gFo7G2hB1MCAWt3
        NcrF/rR5wrMi228e8TuuvtULKgg12fra0IQXgUvRV99YcnEXaHwVZwz3SXwRw+GfYPrOd5Cdhw1J
        u/Ut/85XY4jrrcocsfUDNBixrUm4avDi/naCeZumImih2DBvVN98FNdKC6rHHxTyu1xTc6dGQJ1y
        YmFv2MFy4dwqdBjcC1nX9ivpB+NdgRzob+KtbUAD5lREc+Z0xF/yV843588IX71i+JXaweiwU/x3
        3vU9nrV8F1sZbYzsQpWXlQWTfkhmUISM4La5Y2tykkWJeq0HjHAc1Oz1Sp3fPJKisJn5XL17X/XN
        xQfzakuDHpLIQVNI33RqpkXNz4diRJfisac/PhsX91UM86G6Yu1xQMH0LrYq0izq0tkMac1XgSOh
        K9JOLEBLLxnLPgJo6iKi6OtX/WdXC5DOlwMWVuWMGumJCng2VcVItvBynrkoRtMpytnV17/z4mel
        K96qSdh+YVT19OMHLux3xE2dfcLLzcmEV4d0vCy3QdBv215CCjgqs0J9Z4mH5DbKc4k4/fqBRY0h
        uvzlJX/JNzkVtHivbj6twowdflojvt8q2N/mM9tmwzofHX9ZoYMUK8QhhdHRGJUOxOPaJMYRnfh4
        uDwctSC9yRzmR4g/yFmEZVxfiOfgV94Wz/KiLfNOxmos36wp81xX/fIe0xsxQOO1FELI5XdIsnsC
        1rzaX3uIC7qhstet8mEjRClcHZN89TupV5tNpMKw3OQsYJsimbcwP0EOzDdbL1QjUIF+Mvi+/yFB
        OrzrUZ3DAu5StiT+w7Hyv/NaNRVMuvz679iVexUiplLmCaEZiOXmUYA39iNxzPCd02C1xqBVksdI
        1w85bygO4XOLX3gpHs5omr3UBFxnHfOp8bKaNbub6Jsv2enxjuvZWNz3f+cxG7RkHWPdB8Nznd2J
        rocTmmu9LdQ+8XziOksDSd4tpeozmlYEM7/JOVF2tnZ4N5iFnpNzziLL1NZRQsl3Pt193ydlcAqn
        9C8P0nirmFD5C4t5xrpGbCqQAJE+U+ZIacVH75jGKszRTHQZspoPcthDSl5vLAm+ZVHniSX0PT+z
        z0Swvtd3QRu/lInubZZdv2IrE/bwWuM6bBb56Dxz+m8/tMt3Pgup3CCBjku2O+vPZCZB94Rv3mX4
        JFr1VNmBgLBKZroA7cbZpZwq2Mm0ZmvxcOZffX1CJz9OjKRDlc/9duWrYqzfmb9jqO43vWf/9b9T
        qPuIgy7PEA2vM00uw8Gii0P0Bn9Tr4i3zefvPP2tImRdzyyUWr1bjd3tAhNdV8xu76t8ZCIPoW4f
        C2KHh3vCJDOIwYguNwxetwvmT9ZnMIz1lgTxxwjG2JYb+OrXl6+Hbt7LrgvhMPrEj/cj7+TMK+Hy
        lDHxU21bjy8T6fCd91DNmVn+5YM3wMc/MMNNqmS2PmMFoPU1M/fnjs9xaaia9OQmsY6foe6bizXK
        0DQblj40E/Wr4BqpFf3EZHuYsq4T70aqlvz508Mm6L1bIqqWwjFG1fZe00JWjpAsw9ffvDNLiocB
        vwQFv8n4qOddP2RwUbwNM/R7FUw//3LC6H8AAAD//5x8SZOCwLbm/v6KG3dLvwCZMnk7JpnNVEDE
        iI4OcEBRRIZMICPef3+BdbujF73qZVWFUiTnfNM5KhJ59Tvr/CIAmvLdI+UuUmsWcg/CZTn62PjN
        v7g+ekGZEImMonm3+iY/5jA0dj3i1T7vp6A9Ff+eP4KNZf3x34q35LDizbDiDeC52w5HAvwwysQl
        0j7RxCP+Ye3BfDjET03INy3Fx/uUzO+8i4G63Z2oBecKtKzsbtA7tibK1vkN4YStCd/NvqIR/34l
        445NL5herzYBXH8DzAk+jvrLN14CFJLpUDxy+B4Di27nu11O30PPwbPu69T2nqo1rXygrf6Non0z
        g/FzDFLQOjf2y4fZOAA6/fILNEfS9O+8OpfMmkZj4NY0j8p/56colmVGW+UU/fwX4RqGwEA1cYJC
        1lAC3C9ik99+Azi1vPbTu+V86XwILzufoyYLTmErUd5UU1mXqHN8fsNpkbwWrP4Mh7tRLGfRzD3t
        U90xOQXSJ2Q3+ryAnx7MRHiwpJXPlAPfONjhJQH0h/r2BJgeT7967dlLv3vqB9sp0ZrbrR8geT3h
        77wWfPuw6ZG1AfTt8YPd/UlPNj0KLsrPD9qFlrH5wPsNMEWOoyG7MfabDwLxeByo/XGifl7PD4J9
        9qB+d4hCKfTTBu64ZIvkULuVUx49OthF22Sdn1/ByOWqB5M7Non6vNzr9XwJfJvNgbqt+Krnfnn9
        Oz8M1rz07/X/+m0F/Nf/+P/YKNj8vzcKaiX4ICX3k5KcA0WH9bgAugUUh0tlDBdoaN+SWj1uwxnq
        QQXGg1xS/FKe1vQahQnG73dBluvJrdtIqWK4jdIbdifBq0W89V+g5swb9rqKA7MVdw2UNQSxa5/j
        sGH3VwN5lQVEss+xNadw8GCCm556J//L5tCCBXxLroFD9ewBJn8OjaZK4EI2d9UMJz5AhapsU0Ld
        dsJsib5bGSbppcaRdais+SR7AxyAb2HrvesZHYoogMk5K8jmFm+tCQtSoBZqvqXl8FWTZfsuBCik
        aYX3X7VPlo0QIKAE6E3N+Qksqo9TBMfz8iUyTJuSBU1rgsN5shA8yzSZSjIGQAYvHiMte4KhhdMC
        wBifKRJFVHaVZ++BmekTEV3LtDbax88hSkVKCE5ouPQgzkH4mmPq7h9Wv7mqmwa+7GdCXRqF5fxt
        Ow88Lq8PRel7C5aTWKZwA+onkgxNLLvOLWX4zu8ldg9HMyTcXS0AO+4Ewmp+ZMvG8GU4RImK+Gpr
        9YSL1QmW3LajhqOJ1jQJXQHWvxPKVwdrCrePCfD6rFDXk4JweX6/T6iE4nN9Xks4aoa3Zksgwdt7
        9QTTYlaxVpjmjL0bs8qJnF+c9snOGxyJLui/x7q35chXB1LbagPm1rMcdVYCFTuFLoExTqkNH/Oq
        6Ll3Z81BdmzhOFUGPeFkWy+LPHbg8bjdkTxAtV6u508Kg+1g4Ptl45eEV5ROXesHo/T9Zu22pTJs
        MxVQe/fye4l7vQRY1PIBX29y3rNY3qtQGYlJpqB8MbIsOwGy/EoIV5Z6TQ5LFcOX4+3xzjBwOS+6
        KsOkgCek3Ww1bFuqeABIpketsbfYlxWLB8EmD5Ek7V/W56lvYtApHMXhoe3BJMnXGyzCJ8ShpI3W
        bCuPG6zjco8kJdZ7hoDoQZbfCUbJyQXil8Ecmra/x/YB1dZEXYuAauLPGJnzrZybcddAJBJM3d6L
        67mpthD2e+JiXJzHnokDQMC3jiU2tW7oaWl1OpyfvUMG0zKTKmLeBZpKtUOfh3gJF3SwTP6BG44o
        eR3X07lwn/ANOBkB/uPU0lDVEArD7UCktR4EVxwJ0FH2wUHUPuv5ahxvkOe/Bg3lOQSLLUEHFvnz
        TeyN24LBwY0Anas1INDtbv1s8qENlQSm2NtZKaD4m8pANK0TemwKPxykOF3gTdwrVM+e93r44MKB
        4t3kcYSCq8W8wWhhsCUGvhdsANOmlkT1KFUnfIvckU1Xv2vhJXcgUnV1X84+zQa4u99norGbGU5D
        TiYQUd3Bp44k5TI8z+lff6jtFde95CwVqPZUpM7lwvqPt3wv4JzObzIy22Ti66THkPOFMzVa+8wW
        V5FvalxKPQFK2IfL6clUCFU6Elm5hckcSaoKc/NWoMmc+4TRYzWoTB4PdH2eydyoWwSD3QvTwHnu
        yg4e4Au2VdvhfFMs/fezGwhMXoOD9nchs4gEWgSPfOdRp0SbnsqVsoCDrh+pz9mbcuaQnsLMVC3S
        S/UOLLVVCWAbCwk+ptEULkgKCRSGy4Ei1WnrKei7G+wTziC8vD+FyxwiCE4y6RHrNh6bGTm/4HWb
        ehSfeZTQi9RW4DyAjEYcv7DJ58IBepP7oFZjdAk9PZwndMN1XHSdHbC0b65RxbwOqfU675Jp8T1b
        tUIvINMrJuUESF6ovr/BOBB4PWFPXYuBm5ofjNpMtSY9LhvYal1Mjbhvy+VR1gM8y4mJZlwS66+e
        wEFeHc9NqIfJ82VV3EwnjA8PsaQ5GvcqfzqM1HmTIJwfdm6ChxFBNLn815oipt/AUfFSmtJNHE7J
        Vb0BSzdNGqmgsOiYPiuVChBT38zMeha8vQx39+tMZL769NMQfgIo4ed2Pf9jyQpc36DfOQu2HnWc
        LOCUIXirdR57x8cA6NM9OhBxok7AXieMap1zg7xtMOpthJmNkbSo2u1xNslkniKLbrvtHj7sm0ge
        bOkZs/0GqWu/E/7wEBOy+Ziv3+uR9OyK/quplQBP1UvF95N2YOv55jB+fwocXdI+JLYNJuh0xpFo
        xmbHZrkjLWi2wweH5/idjD69EWBf70eMHEPuh5Q7V9BT3gN2nc2u7s6aksGHKSoIXDw9mbzutVc/
        8uFI/WoXWfP3aBINTFWK83PolGxbv29gv50rGj0zN5kuX6uCotWnRLnx6/OT+xh83dzAV9cK62nI
        mwUcx6mlBt6sDrVVMyB0Qkqmh7j0YwVnqBmS//nhKxs+x1kF087WaVCHMpg+z7mCHWiG9fm4NVPS
        KYPfhd+QytAGRpZo64GMu4vY+A4ZWLL2WAHUZzXGO4tLZjUKOYDlWcIWbJW6LQx0AY8mzWi0Sa7h
        oOcgB8Ux7ql1sq5s/qRRBZpXtsMhU4SyOZZHFdbxeU/EfmqS8QbYBKNjnKNnfGWMJdv8Be3BAdiq
        36hfnK0CoT1xDJ1E0Qbv3U0RQQFNE+Wn+mut+mr/w+u/+puO/bRXm04w8OUWo3r44XXSZBZhXHRl
        c+lINjgfHj01NNvshdK0W80YzzL1LoZRLuJGLzRXqmzqorsbfml85QByJQf73VgllN+G8Idf1Cxp
        kLzV99aEalryRD2IVTJHc+LA0xEjpL0CnLBqODUw2nkN4lL+bZF2ERE05+KCuDRs6vFO1VTdB9/T
        Wh9DyF7HWIUyJyp4rYdyLh3eAfoAd3i38vPy4fgW2EI00nJKEvCd3loOKMtiRI7nVzldsy6GreC5
        NFTC0Bputq9rY6dH+LTr23qKhqpV6UyORNQ/Tv2dncsTvEw1o1GnV2yGOzOADyo+6PbhVdaSmakA
        WsR8jKC3Dalgcx5o26lEc2srgFXKCUGnSj4UJ/czmErwWsD9IHtIe6RCMn0uZwJ8X8J/9TIKpZz+
        zg97cT8D5jseBKYYJ9jecR5jlXOT4fuJAzJzsZ5stI+RQz27nOn2emvqZWgVD4x95+OfPutyXxWA
        YyUc+aCtyyTutjOhG+cdRbQRLLb3BV192BcRSdY7SUZWLAEAku4h5XNtwmHyDBla2m4hyu1NwBy7
        nxuUfP2APTvU+gEXvPqHR9L2Qxnp0uQJA10MEF31lZhfi/jvZ35nceV8v8Qy6DihwftlefYzkmdH
        6+oLwC5SmoSVWuBB/K41/OOzRSx9D2JozDhoDMaGa/aMNQReDsby/mQNx16OIdjvZyRq/omR2mpF
        KMyZjzbCfWN1tebBPz2xm8WttYFOl8FLFREkBAT1zJ3rPaR3UcD6dTkDlqn4pXb+/Yu3wv0Yzprg
        6+CIfZO0IjqE7Ny+njCcPI58kwNgM7rLJlC5co82+b4FY1d7T9h7zRFNhk+Sfl+DPaQ9qrCdibrF
        iizcgyNnhuQjXt414XeHAG5PQ0ATXr8kcwf9Bvz4YSK5VbLy5F+gFU0uPe3Bc9XvSQrk+o6o+66O
        PY1fyg1QZ+tQ81L7oeBzIYHNwUlQc4yUhPVrojFwuUZd1nQ9sylRgTLYW7pdLKVkV7tCGjh6LU03
        wC3X/ulAuilTJJ/DJiEiaRsQwLNGA92ZAfViMweWOHKIwyMpJz+0CmhUPSOclpls01Q7CO+62WLL
        zcyS6Z88B/H2JFE8RVxJf3rVPe0f1LN5aA2mm2eQNtkXB0HTWAuvzC386SXFeNphF4m7JxS7osdO
        mhdgKfrcgUtoFhT/9FuyVwXw5Ud19TdvQN2tuYDy8xGp6b9hT+mzTWFioC/GHr6zYe03eEk3Jl71
        Rjk9+bsA+Idxod7yOvXT6kc1Wxtkwj+tT9jvM9CpygkhogpvI5yCxdyDbKYz4s8hBWQrL3tQKW7w
        Vy/sfr9zIGytCOsfeyqH+AL3IDkNVxrDCwLT6Gyf8KSmLXVDPLCh1m8OJMv7SyQa9eV8p0umnTz8
        Jlda+AlVUjmFq55BSqoo1gJeVwFuLy+DehnteuYc7AgmptDQLcd/yznqwB5ezoOCzevpU5MR5o0q
        dnlP5Lif2fL0gxvUySug93ye+r74dkT1w3uEuNVvzjD5QqBNdk/NyJCT1832TXhwKn89Px6wIXll
        UHKLlIYatthimHwBw2/v48DxkNUeWm0Ae98qaNBslnLevc8dXPsJTV0a1GQ0p4tW7DyF7r75LpyO
        t6kDq/7F+D1WPRvH+gXqVzyR+y8veKNShUlt+9iYs30yRUq7B63VG39+efWbCyy7zURNaNJ+CS9O
        DPzkxWFbCY16cc7nAt5269LmliAgcBkWgXBwGiKY3YaNsfu5/OqLTN71kYxXdHV+9//DA7YErUvA
        /e2ZhEMKZT+9AqsP7qh5wJk1RbruQefMlhV/JDCa71AAaVv2SKPFt2SahGxo78c92oAjBotkeg5c
        /R/a5J1nMVKaqRo99wY9DHC2JiUCHljxBVs0qdiHgEfx4ysckO3Qs2IyK3iZLgeicLe0blNpHoAK
        /AybkZEnDHmaAFzFM2g6XnG52PMlVn2bz7F/P+/KNV+YoHC/tOh7HKWe1IHXQRS6Ova/2zohn5zj
        YNlJEw5qXkrm3WFBgGykmqh3wNX9LpVieIwLjdRrPvK9X2JVe9TOhcwGlcuxJeEEo6QtcQjLdzld
        LpYKwktfkTF8WP18vIYxYE98JQ9gNtZoxd0L9tu0RH1qf3q26m04P78OEqakSyYHEwHGIw2JGjRN
        SNmVR/DDzjccHtDQs711QfCS7LZ49S8Js2FTAWmneng7lJdkMhvlz+8QwH+afl7SOINC8ZpxxJ8X
        a44JSeHx2EnUvSA+fJFkG0D23F2RzHIxZN8bbYD4Xjd+3O4esnieHXUbDA9qpKMNaNwf9vAUXu/U
        0F2QEEEPB+i6Jkd++D+De52D+hJwf/X+hc4z/fEPDaxhKamC8grKufukwVLbPfv2HwSAP0EkkWUI
        p1U/AxXOyZ+fpy7XqT9/hfWHuNTsUd0CQF/D8MfvMytUT42lm4oAbnDJ4tu5BaqkXMjC1Csj4rrR
        Kj5jlepd2vXL/EYRWPMcjPBIkqUC9gsSeiSY8uo+Yfw83ODqX/B97c/lE/IpYEcsYLPaPft24D4X
        0J1pRDZZ29dLl5ZPkHw4nXpxf2BTgZANm/vlSC/a6cgo30cdbIrmRs0n/+ong1djwNDI0y0XBoyN
        bqeC+3nJsNNPTsKudotgLVXdXz4olZoZwFtmZNRW+MoaSX/SoUvzmOKfX3G1bQVLzu3IIaMPi0LL
        fcE85xC2p/Hb09dGgcC3tZzi57UpJ/CRG/gejzE15M4qZ3vncbD5NC4adhlXE9ZEBWzM20hk3vGS
        zVXVXlDk909stLJcDmD9BAtpNyqB1kEPhbV/Rcwfeho9p6ZmjyR7QpWEIxKN8h3OPszSH79RvE/y
        ngZNq8OfX9wtndUzXzFizW+En95+lN1Pbz+neqLuJLT1JN/TXFNf3yfa3Ksna9P5IINzaYmkE3i9
        ZEk4FXD1O9QrznYo7BF3gRtSK0T94EP/l9fVb3XccdB7hwtr7EKjBadRfdL1etQEw4TgaooIHoWs
        npL9IoDf9XjUl+WsjY9IW/MQNBs0TyalKhoQOuGAd480TUhXsAwOMaNEEOmbdRnZpFCxhpk67tVI
        xtlJK/i4oRcO5mPDRnI4LdAoQo5I5iwwYl64FvCfek+mVR8uu6lW4QY8nmQRs3NP7AMoYPK4RFR/
        mEI4dub3Aq7WENJ4fpZWx+/OAVTTM4+3ST6Xy0HRnd/5ot7LNuV3xTNobtmODHItlcP1ajqwPiUu
        YeH06Kku7HUYnsuGCMZFDqcLrVNt5UeyhBpfMs7eLpALbxnVH2JcTwdzY8LczQ5IQHlrrXo1UI5K
        kFJ7X7z65ZscMzCHCsEeTa/lpCvFDXJedMFX5zuULFPdBp6SzwG12TEslS81oNZqbYzRyJ3CqUCO
        DTrwGug2jSZrya/xHsxvoBO1KRNr/uWHigP7P//IfnlA+TpC+sPzeTtWJlz7FXFzu6/n082/gVU/
        YS+5TyUb066Ct/R8pqYqHPrJ1KcnyA+2TsMDiurJZJMIDufFIko3VuV8tCJTNWowYte7Bmw52+cI
        tPrZxZ5y60viOlMAZxy21O9GPZH21/ICbrXJ4+1inUuWbPcNTAg6oOd1dpj0vsgy5D+PPXnIe9ua
        tx8daT9+YBeaAEYv+hN0FTawHaQ06ZN4HwG7u3GENeW3JjaXyBAkz56A095jQ+tPNzjJvYB8bS+C
        peU18ZfPEUmWjXL1my+AHkJOMwdbtRApbays9fjTt2wZjocBtNA84lCeezZ63l0E462rCKdWH/BN
        wMgpiztVv/6tNxzysj+8ECz7aM3CM/eghQqbbEqzA238BbIazgugppidayZI3A3G26NEA2l+lpMU
        Xybw8osA75pnHH42y0UA8d19I/51f4JhCD/ej0+psU+2/cwMxQbN/XbEtq/urYYbVB02bvbC7lsV
        2GwcXgX81Y+t8HrYD8mQQkvhZezKspFsvk8z1fKruCVLquCa+Y4OQVeefLpruR2TDpr6hO3yDKjt
        ZT2jP/2x5m3UW/lws/odmJuXAkevpbaWp9l18O1Lm796qlJ7LkASEIH619PBYhfOS9Vb0Hd/fPfT
        Z7DT0BcxK/7W/e/9V76lhg4UMCI+l2FgXlwcOqkMlge1AnjM5ZmGa/677Py3CfVD86S7Xg8sKunF
        AP1+oNggqlD3b1PpfvMgHKx6XayOlgpR7nwJIAepnNXty4PKKUI4U0cYTnyNLzC/38yfHy8J78yZ
        dnzoNblGR7OU9E9eQEna8H94+DePWv3nOv/IwnG6HxsIiJrRX944R/bygr88bO33hG1kof3lz2s+
        KoUkvKA92D9ySp3m6dViDpkH9ltWkRfK23C47oMb3MFrgf1Vn83ylZrQUjQZu1JXl3RUe6TYFv1Q
        FD12Fjs90BPCIDap9dDm/mlzpSqpxB/JGCRbMOmL2v78DuEmIbam5/nuqSHuJSKL7ZQMr08Kf36T
        2nv9nUxc5gpwG5AHDvcFCVd8eMI1H8auoYkJ3dbjBRhS+MHGEiA2ax+/gD99J7BtVG4ENA0/vKSh
        fBr6qWa3HBxTXV3P/1TOv7xIdyIRGyL69OxEUAB/+dmPT8dPaldQg8JEHf6h9otan9pf/kzOx/FU
        z49alqFTPx4I7F5JON0PegvX+RACKJ/7zyPJ/viFmnh3rpleTQ08ebs33eZ7jwnNg2Xa8dlWa3+H
        jP7yNMG86hTz1RzS1e+Bxk1fGAXbOZnnBrwA6Mw7DVXWhsMecTe45pErX6Vs/f89dXGXiswLNyZs
        SIYM/PLLKObKmunC3tQCjCIa9puyn1o6B9ra70g+giGcozAuwO9+5qNrW8JPL615wboRisBfPqHN
        Q0st3fGSxhQ1Gaabc0ri2Er7+cS1T3gwuhgJmVhZa36fQaeuH2jSer6m3OmNoH3fxvivXuRqnrTA
        dmxyWM+b9spoQgrFK9bfzsDmBLwhvE4KRFKb7fthfJMC5rvXExvHh/VvPh11E6z53yccrSYwQZSp
        x/X6erh5JygVUUQ0xHhBZeMx6y9Qf4hbwvPOzEhE3wSi3P5S6yTBZArjR6EllyalFtnWYdMuYgS7
        amcgkvLv3zwyh0b1ZRgBsu2XevIz9TdvXue9YOGTiYPuuByxxzttQncPTgbHUPEwPmkzWPPbVDOv
        KkeWJ/9a+Voz4S34dtix2Acs5lfcQ2B8Q2oblzwcs6kjf/1kBwTVY2jB/MffSGzgB7DNkgrauU/O
        RHgcr7/6z1X6bWrsvh0xWf2O/PPH2Aono9/85i/93fGxcfKrpCqQ4wDTN1yyoR+tHh/aFMCj9Dyh
        OT58reX+2GbQfej7Fb8e1my+LUFd+QX/+GZe9EX94TlZ9Zu1ROK2AktWOBS9HZ5934qrw2Gor4Rr
        syJc/aYNL+B1xgG5h+DXb0AsQo9w8Pkqv9NwjOA676UIkHc9XS6hCiXfPPzN64k7TCL45ZcB2Ub9
        rEYW9783Cv7xz3/+z993FjTt9fZeFwPG2zz+x/9ZFfiP4lr8hyCIf19sQIaiuv3rP/+9gfCvb982
        3/F/je3r9hn+9Z//FP9WDf41tmPx/r9+/Y/1Qv/1j/8GAAD//wMAjOe+STBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c06ec39e6e-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=mACXFdzUU5gzexc8CNhknuOeCkPvi2.Kyt6jXreWWqY-**********-*******-br8VVQQdkEoEgc0jo246cgcSTuHzxZj_cHdgJ1uO_BD69hKbtayvEYoK7Hy3vPuWcJ1C3EGHKLND68GVNOuMMg;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=NJaZiQKL21ZBfqRUZ8SnS3LjiBoDe0CTOT92mUjz28o-**********379-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '18'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999998'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_50b04626d72e28d91cd233d90d4d9065
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [9642]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkka
        Ye+1vrX57//48+efd14Xl+Gff/3551H1wz//+f3tmg3ZP//681//8efPnz///fv8fyuLZ15cr9Wr
        /C3/baxe12L6519/xP/95f8W/evPP0oPCr3k97QbBzlxYSnXOts24aqbBvncQOQpPvMf7dTNl5Lb
        cN4uW2ZfzUcyGlIpa8w7GHjyLms0WlwXYM+YhmczxLV4Dm8ygjmeCV7EPJjU22xqUYxtYhy2t2Aq
        l5kASXq4MWdtapy+DvqonYgzMGd1fyZcV9RU/Qjpm5hS6gXjUayP8Nj4N6o81GM9VceLqB6uiwUV
        vPMT9fvkUyFbPT7wbE41Hxv90cBCxyEeA2GH5qOj+GDHVo+lVON8Wu+9GAWLa8/W5bazuMTUPciP
        Z07IfGgttstiQR3ZsmR6/+ytPi3UErnibOHV3baTcTDKCmYF90S/KS8+jhHpkWCAT65k4MF4csQY
        hOVDo8rxKFlU6IwL+G9tS9VDZXKxeNYNeDZ7UVhvTM5eY/KGcXi+qCZvtt10Ll4Y0LnQmIEfn/zd
        rd0Lip0cYy0b1gn31PqJCipZVNmgszWnh9aGK416EqYODUYcjIXsbUygaFYf9Wg2OEKP8zkl1q5s
        Lep2YghGxVJmaO8yGadwqpB6fA946vL6ez/mPfAbjihfn/b5uBHOAmwf7ocFZHFNOompR2AqQ8Ss
        rkkwmU1IYXhlI9mu7WPN7+eXqm2NS0anzemUj4swDVElQYgPx+MlnwLvXqDHxr2Ra7t78PF1CGR4
        XBdPOg7BPZmK/c4FeZvcmblYoYCag0Ph+zzwaldafOXIRxfAN/fEeaVRPe7qRgBzu/OJpyz7bvJV
        2sNnGYfMXhhmJ639OUWdaHlka+J7MGaeQcENDE67o91xengMJZQfc8a7fGXWg3DXelWKeIGV+X3v
        eLl0bOgKR6dKUuo1X57ARloHD2JVs5WPnjrrqHDFG1nzqcvfa0MR/p4PB92ARsfXKhAC50mwtZeT
        uVkdbGQqUYIFV5y6QRZkF143SyYhUDdYJZ9AhlPIUyxXLOK83NxMtB7cJcFXuQvmt0l92GnRxNyu
        Fet5s/RKKLbZQMxL0AejOSQNetxpTLZd9smnWFiJ8MCmycySZcmYqlEPURW8SRgqVjA9Wu4vdBkb
        jJS3qub661OhWZtVEmLTqZe7uk6RcLcD4lBjyKdVkFO01KOaednQ1ZRtPF3Rm+uR6X6kBN0zvWYo
        3XsrRuaDH0yLUc7UOImOeO8v3WB6iO4FXsYBM3d5t/JxkHMXsjz28GxYgTUK2qEA+LgH/DjMOJia
        1bGFOSEuwVqTJb0hlSpI69QlacUiRNteixGf5ZKkVHbq8duvsOkAY3jeWzTslhCh8zJ6MDKcFzlj
        Z6eB8DQ/ifG8pcl4y3RbO7Odjpfhqs1Zo59cUMzzhnmzFSY8k9o9+tYXzZPZrlfCfW5U4e4ETBe1
        ez1v2ztFLnlfmX/dW8FS0HIVHlftSbxz87a4rsyZZgsyZ9vTLUnGDoc6ks5Ixb9+b+U+Ef7q61Yy
        b8GbjFkI+ZqXxLmZVTCWwkeCZFemBJ/Eupvf5tMHUitPFmiHqhvAN0Lo9orLCN4J+fytd3U71gt6
        QHXQcXhkFayM/ZLs171dzwcjmGEnugeszFafzDU9V2hHdMac+jkmrYhVB2WCkNJ8O0hJv+kNG85X
        c4MVC3RL+tRTD+ftqmWB0Al8TMqtDspWlJlDn2bNbpv3DC20LdUehzzgyHYE9FsfhkptjdWt6yGD
        zqJl/wwDDpc1hpaXGCO2EXLWUAdDbwwicwNNSDg740ZdePs3O6P6kY+SuevhbJ4VPGvGAzF7niuU
        R4dkkJXyktDMFUVoeYWJf2Fb/vC2Xqru98f373g5/ezeEhqbnGHxVIUBa/LlETI181mg1GYuCXfR
        R/4UbRi+Jwc0mdL1iYzT3sS8G17WEH+iEZT73cWjtzl0f/utoarBtvejGXzryQFybwdC9seQj2fV
        EWC7OlfM3IY6ooKWHdHdSCLciq2F+myoi189/rbz1d3WJW2im4pCErTWJO3vJrLqqSXOdf9JRvWj
        q/C2vYIZ0drs+szzehiE7Yr4evFJ5t1y7tVPLUrktFk/OXvcqx4+zoISk7E+6S4fzVa7wtbJ2n/U
        1jwPMkbLc7rA9+dNzimaFk+0iwqHEKVaWn3x7Br43g9yCKhTT6ez9YTyph7IhvUKYguN6KqXFvHP
        r5L+mXYS3D/1QNblXCacBJUJUjQVzM3vBeq3fHbhiSvt9/z4fNK6UR03L4uQQRDr4T0WM8qmbUzF
        rlXR3HhUR/l6KvFw2Ib5nE56pAlwK4kr+7q1cqNLgaKm0Oivfjgx9z26SKsZr3koWXNXnkf0vR68
        grNgUfVYu6AJpCC21A7BMBjXEDF2fBBvc2r5KGjXAr3v+pME02RaHB1vItwb7YbnoN/Ug5EbPno/
        niJW3cjruG+9MKxcnBJzSR41jUtPBdkpMixw75pQvljtwdn0b7LxqFXPTKR70F7piZhiO+bzHMcm
        lPp8IF65tdEgtjsR2BN7JLw+NwHf0yn+6R8d004Khk01Y6RbzoUQvCvyQUURhmRWYqbflA1nEpuP
        yOmyFcPm2AbzpOY9zJdMJ2Qa92g+rKTs188sfLU06XleXVAbOzKxk7HjfaMPDSi9oLBwhTH68QHo
        YSiTjfsorbGQoQD7HnoEm8GeD5+aR+jwfmLyvd8Be9zbHm3SbU3cN0/zaby5EuxE/8DcxaPtZu7Z
        FIhZ9eTHl5xLM0YdRjXWnG1mdZfP0lYrX7NYseSGxQ9JYkOz33HmG+apo5JihOpPj1VOUzRPBwW0
        22B9iHe6netR/bgybI0iozIZ191cBGYMz9Mno0oTary/lgKGSyS3WIBc4rR52BjkOnGIrYddx7Zr
        eMPYnBmmT07Rz39VL3j5bL1etwF/vVIbamHfkkDtH3w+iu8QBHtrEVOgq3r8uGUKMmkk4opAA34t
        2YwkTziSkF1oPayNMYUqW9vMgUubzNK11tXLZZ9jkcohH4t93KMu2qtkK/cm53qxD6EwBGD6irzr
        MZPk+fedLngx1IwPvazWF7Sn4qsz+Gi6kwS7wh0x+izuqC/PwlF9vfYzc92KIy73WQZaWYoMP4le
        T4NfVdqshD3me7TnfAuTCffCPZFwxe1g2dmaD/vbeP7ygdmtHP9lo7dz4SR0omc+DomSwV3ufRLI
        5xPnkhlEsDxnC7I9qkLQc28jgpJmHTHv8pXPnc2fGnWqiapLPAc0VS0JhMlgRA8Pr5qvnoOJdoU/
        YtU6rOsR2dcU7NjoyWaa3smkj4rwq2e2/vp1v977InTWdcZau3ugqRSOJXoZJ0y8m8QCOjqLGK2j
        HaXP6SJ/+/0QIgvsGq/Gt825Z8kCJL0y4BqmktNsKI9IZ2iDH9HHr+mvX1dlIf3tD16elTcKbQth
        fnFvfPjx0M9fnFc61ryM+iNqr9WHLtCyDvgqwCLKT1hiBqE94rVeyoCk3YeZ7jJIZlj1gtrr8u7L
        43ousm5QEZO8gemfVLW++0uAl5+G/fiWizyO0EK4bPHk4ATNZD+qf/mi8LSlxbJY/7d+W52749P1
        xI4/3qAqXJqAc0nFylrf+1T8pBxNm0oNUfPWC2KeItda8aGRtZE34Zc/zGDcnOUQos17x46Wklnf
        PADw2C99LBuHN+rh8RZAVY+AkVKbyarJl3vo5ySgg4kNa7yc1BbIu4qIfoi6nJ6LoYUwQhZVVqMQ
        TBe+rmBD1g7bFIsD4qv9xUXf/mFYoDYa5Tov4VsvxA7Ox4QvyE2AwCAd0dn1nc8r0xbBNdAJPxfJ
        sx5iYSGBuN9lWPC0QzA1NHFg5YYpsWr05IN1umNUt68FRp+sr6eT9jhCKL5iElQTQaN/dCrQ3PhI
        MPeuOUeTZKrN5CNmao99MH/98S8fWrvSD/jhUQuglZVI1nm8sQZPHQtN0dfzXx4cpgMqwTiYAx1f
        3duaIAlHkEcfU27jCPFSyDP111/bUa26iet+D7UyX7Cc3wvOjgT5gKl0wZXomBYfLnIMydiemRm9
        go4XBs/QrtFkOpsjoNYu6xB9vMRi68NW5n0er2MIosxljhsxxHns9GoYKRadBXqquVmvJPj2A7Fw
        LAefJ9kLP15gtl3frae8/ETo+38pLB5NPvPFYg+5f6Bs0w06n8/W5gJpMa+JqyV1x6O6jNE+NhLm
        WIpqzdUxiUG51y5dnKU1HzFxWoRUOSH+tx7nR6s8IdgLRzzdUfv1NxVDmIQp8apbx6cyUjB88y7z
        5rfRiZ9Fbv7yA+W/fkXbq4hKXCyY/vPHgFr4l/eJq35uvP/lZ/1s63T88vrIukGGw1VbMKO2t/XY
        xMIFfvpsvN454tL1foHb+5qzwF/p6Ju3XNBhJriThSEYyzpoAaSlzjZOOdXdQhlFYO14YrfrsuCT
        N9UuzPZ6Q9xT9UBcxPJ3XhD25Ku33XJ7vwI0ZNaJw9gz+F0PWr2OZywOvmiN70BqYJs9pS+PVgn1
        ps8TGXIPxDiVQ8BELJewslofi3rY1Uy1I6oVwvQiRjk/u8/B2B5/PE5yPdcsZu6ooPD29cTPWAm/
        eV+pQHaHhGHpRHIOidfCl2+Z+ejWQZ+Uax2qQykR86rYSDIby//55c8/0Vz2tgNeeonxEtVdPS3G
        MQViiwZxzaLPmRyJIVwfbkTn6sotrtRrG1T94zHHHKWOohulQFuik29+7ebLaZjhfVI9+prVjo/c
        IyJqZedC1plX13PmgqQuT5sWS9FTyKd08iQl0z+IWHsk8vlJkky5XlZHZtn8GbBd34l/rz8oFqQb
        9485g3O9IiS8ydSiU9i0igrv3XZU10XQO1cnhfOu+e6PI97JfZaqCTg+Xubxy+LbdFsqy9O6xbfh
        fMv/8tllThrcHjsnnw/+Zg+pJjssGd+dNRweL+eXJ/DYOTEaBacS4eAVWyxc5cAassU8gp3jhrgD
        syzu39YZbE6pzr58FXA9tI5wq5UtnuEydl/+NZFnFhQL4trNZzNbSmiK6ZaR0aqD2TsGmfrZrnbE
        1cN1MojQUvVNHoTQRZxYUxODim4QOuQS7QfEhssYI08PzsTg066bfvn+L5/rI6/Zz99//R2+nNL6
        8TcE0iFiDqe3gH7qiYLeHRW2XdtSR4fHMoMFO4bM+OaH/rK+z9Cj/EW2n3785oWgQbtzUxG7a1U+
        OcazBdn21swvrk43L9pFr3bRUf3qfcPH4WlGYHRLEZfLO+Hcn44RKLcIMzMypfzTfwoVfedXVNvm
        YfDtl6fq77MFC2b10c0d1lVoUvlKzMg8JmN3Oh9BlW2Lff0i4YfL8Q3nekmwHJs94vtLmWq/fprm
        N7WGztZc5bc+eKlJMi9SKqFimw4kpLKeSxZ4Kazjl8UCp+wSxpxlD73iUmbiQ5P3umUXWmrIAgkX
        OrHmX72/aHggTrw/WbOIVRuMzxNR5JRBPgvOFdAyvl/wyl9tginLd6b2ux+2XRvBcptmKaSa6rAw
        Sw/B6C7Dt+Lp3vk778PB+6iNrVYvQ8z8RXxO2KVUKjXWJPebj0prVNF5RmyrEPLN66jzprcOiiIa
        xDJ04a++qriNMLmZ5SPoH04sgAVOzZz3fZ8zU8oK1WuHgLl6+EimT40idDj5MjE20NfzllilcjJS
        i+7Dg5Hw73wEYvrp/p0/NtXYAk/GGyHPUEyGo/iZ4fVpN1T0umvH9HFw0I8vouAMNSdBa6IrjXtC
        zHId8CmsHTiuMxWfSH7IeSkkKfryM7Hxy02+PNbCt/6pKkGLmBtdLmiOBIKF+gFo7G2hB1MCAWt3
        NcrF/rR5wrMi228e8TuuvtULKgg12fra0IQXgUvRV99YcnEXaHwVZwz3SXwRw+GfYPrOd5Cdhw1J
        u/Ut/85XY4jrrcocsfUDNBixrUm4avDi/naCeZumImih2DBvVN98FNdKC6rHHxTyu1xTc6dGQJ1y
        YmFv2MFy4dwqdBjcC1nX9ivpB+NdgRzob+KtbUAD5lREc+Z0xF/yV843588IX71i+JXaweiwU/x3
        3vU9nrV8F1sZbYzsQpWXlQWTfkhmUISM4La5Y2tykkWJeq0HjHAc1Oz1Sp3fPJKisJn5XL17X/XN
        xQfzakuDHpLIQVNI33RqpkXNz4diRJfisac/PhsX91UM86G6Yu1xQMH0LrYq0izq0tkMac1XgSOh
        K9JOLEBLLxnLPgJo6iKi6OtX/WdXC5DOlwMWVuWMGumJCng2VcVItvBynrkoRtMpytnV17/z4mel
        K96qSdh+YVT19OMHLux3xE2dfcLLzcmEV4d0vCy3QdBv215CCjgqs0J9Z4mH5DbKc4k4/fqBRY0h
        uvzlJX/JNzkVtHivbj6twowdflojvt8q2N/mM9tmwzofHX9ZoYMUK8QhhdHRGJUOxOPaJMYRnfh4
        uDwctSC9yRzmR4g/yFmEZVxfiOfgV94Wz/KiLfNOxmos36wp81xX/fIe0xsxQOO1FELI5XdIsnsC
        1rzaX3uIC7qhstet8mEjRClcHZN89TupV5tNpMKw3OQsYJsimbcwP0EOzDdbL1QjUIF+Mvi+/yFB
        OrzrUZ3DAu5StiT+w7Hyv/NaNRVMuvz679iVexUiplLmCaEZiOXmUYA39iNxzPCd02C1xqBVksdI
        1w85bygO4XOLX3gpHs5omr3UBFxnHfOp8bKaNbub6Jsv2enxjuvZWNz3f+cxG7RkHWPdB8Nznd2J
        rocTmmu9LdQ+8XziOksDSd4tpeozmlYEM7/JOVF2tnZ4N5iFnpNzziLL1NZRQsl3Pt193ydlcAqn
        9C8P0nirmFD5C4t5xrpGbCqQAJE+U+ZIacVH75jGKszRTHQZspoPcthDSl5vLAm+ZVHniSX0PT+z
        z0Swvtd3QRu/lInubZZdv2IrE/bwWuM6bBb56Dxz+m8/tMt3Pgup3CCBjku2O+vPZCZB94Rv3mX4
        JFr1VNmBgLBKZroA7cbZpZwq2Mm0ZmvxcOZffX1CJz9OjKRDlc/9duWrYqzfmb9jqO43vWf/9b9T
        qPuIgy7PEA2vM00uw8Gii0P0Bn9Tr4i3zefvPP2tImRdzyyUWr1bjd3tAhNdV8xu76t8ZCIPoW4f
        C2KHh3vCJDOIwYguNwxetwvmT9ZnMIz1lgTxxwjG2JYb+OrXl6+Hbt7LrgvhMPrEj/cj7+TMK+Hy
        lDHxU21bjy8T6fCd91DNmVn+5YM3wMc/MMNNqmS2PmMFoPU1M/fnjs9xaaia9OQmsY6foe6bizXK
        0DQblj40E/Wr4BqpFf3EZHuYsq4T70aqlvz508Mm6L1bIqqWwjFG1fZe00JWjpAsw9ffvDNLiocB
        vwQFv8n4qOddP2RwUbwNM/R7FUw//3LC/wEAAP//7Nxbj6IwFAfw9/kUE1/ZCQhKy7y5oI5cBBlF
        2WSz8QJeEKpYCjaZ774pms0+7+PmvJIQkhPS9vzPL52pVU/0O2J+4SCjf5n5/VRlVqPEE4zv98gO
        vrfzL6n0MtyrKq2iqplaZR5HMXa/T0tf1su4rB2yWj/nj6hrWY/9T6y3VSjWm5tYb5AsJdPAU3DB
        GVfvnlF4tezLB2uGmjD8PBpK3CUsiNJ63pzj6yfSR9MVs3CzR4RvrgmeRMT0l2J+U0nKyMTnfLZn
        nnzO5nTK6wwvdrthhaQyQXzsFGO9zTcyBSvzOlwfYnymjsVGTTrc1JewlPCPgT1gw8lRt2qxHxii
        f2P+LG8QLSJngcg44W0+zOkNsbrNL/zG0+pnXh1r5ol51Pk4sdjbPPNT/7PX44z0V17bf1VSzn10
        Y4ZaY2WZswp9XHxe2+Ti4JrIRnve3TTbq43xdmpLzOTOyiUak0190RtobBwdL2591yYEif4scKdU
        3TSqGU+MYp8G1crRCpcn7LhF7XlwqeLQ0sR+1g/lfByMZU1BZXhKjihg0ar9X0ueDdKJXgTDRWXk
        SVLecJUdcVuve5AUvD4siYPtIS2Cj9lqMO+WvrPtt/3gcG0seRPKdo5MVZKYyxPO2/kgUqPoxobF
        2CsbUT+MZssDs6+h52quvcjxVJqP/J5rJJs69g5XfPVGczE/3yEqxfoEz9PArPTjNj2J+lb4bOYh
        +yBqdmrKe/bMDx2Rlz7e77Qq4OvbP4iCLogCEAUgCkAUgCgAUQCiAEQBiAIQBSAKQBSAKABRAKIA
        RAGIAhAFIApAFIAoAFEAogBEAYgCEAUgCkAUgCgAUQCi4D8XBS+vrz/bOwtyskvOAgbQpKFvf6jA
        23q3flMU9XGxQXVb75PO+1MgdC4lyS/0FyVZUtw676/qgxp0KKHr81+PX8SHvl5+AwAA//8DAIpg
        UmUwQQAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c07c2667a8-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=ss8Dfnw2dCnMKMCi.zfWS0BJk3Nya2MozNAf3ZEbAFc-**********-*******-7VuhrvtkTtQGdmJrWFZXfwA8qU6jrfB.YvM3fzxOi5ZpTHcR_wJ5SDu__rNLjonYngTNON5JGX2h5xHlZPDQSQ;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=J8niYZvhXxO6enLta_jyadIvgONlgnxKdomInKFTRQo-**********391-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '17'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9997'
      x-ratelimit-remaining-tokens:
      - '9999879'
      x-ratelimit-reset-requests:
      - 12ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_8687041c6a37dc7f1917a53a9eecad5a
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [2822]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkka
        Ye+1vrX57//48+efd14Xl+Gff/3551H1wz//+f3tmg3ZP//681//8efPnz///fv8fyuLZ15cr9Wr
        /C3/baxe12L6519/xP/95f8W/evPP0oPCr3k97QbBzlxYSnXOts24aqbBvncQOQpPvMf7dTNl5Lb
        cN4uW2ZfzUcyGlIpa8w7GHjyLms0WlwXYM+YhmczxLV4Dm8ygjmeCV7EPJjU22xqUYxtYhy2t2Aq
        l5kASXq4MWdtapy+DvqonYgzMGd1fyZcV9RU/Qjpm5hS6gXjUayP8Nj4N6o81GM9VceLqB6uiwUV
        vPMT9fvkUyFbPT7wbE41Hxv90cBCxyEeA2GH5qOj+GDHVo+lVON8Wu+9GAWLa8/W5bazuMTUPciP
        Z07IfGgttstiQR3ZsmR6/+ytPi3UErnibOHV3baTcTDKCmYF90S/KS8+jhHpkWCAT65k4MF4csQY
        hOVDo8rxKFlU6IwL+G9tS9VDZXKxeNYNeDZ7UVhvTM5eY/KGcXi+qCZvtt10Ll4Y0LnQmIEfn/zd
        rd0Lip0cYy0b1gn31PqJCipZVNmgszWnh9aGK416EqYODUYcjIXsbUygaFYf9Wg2OEKP8zkl1q5s
        Lep2YghGxVJmaO8yGadwqpB6fA946vL6ez/mPfAbjihfn/b5uBHOAmwf7ocFZHFNOompR2AqQ8Ss
        rkkwmU1IYXhlI9mu7WPN7+eXqm2NS0anzemUj4swDVElQYgPx+MlnwLvXqDHxr2Ra7t78PF1CGR4
        XBdPOg7BPZmK/c4FeZvcmblYoYCag0Ph+zzwaldafOXIRxfAN/fEeaVRPe7qRgBzu/OJpyz7bvJV
        2sNnGYfMXhhmJ639OUWdaHlka+J7MGaeQcENDE67o91xengMJZQfc8a7fGXWg3DXelWKeIGV+X3v
        eLl0bOgKR6dKUuo1X57ARloHD2JVs5WPnjrrqHDFG1nzqcvfa0MR/p4PB92ARsfXKhAC50mwtZeT
        uVkdbGQqUYIFV5y6QRZkF143SyYhUDdYJZ9AhlPIUyxXLOK83NxMtB7cJcFXuQvmt0l92GnRxNyu
        Fet5s/RKKLbZQMxL0AejOSQNetxpTLZd9smnWFiJ8MCmycySZcmYqlEPURW8SRgqVjA9Wu4vdBkb
        jJS3qub661OhWZtVEmLTqZe7uk6RcLcD4lBjyKdVkFO01KOaednQ1ZRtPF3Rm+uR6X6kBN0zvWYo
        3XsrRuaDH0yLUc7UOImOeO8v3WB6iO4FXsYBM3d5t/JxkHMXsjz28GxYgTUK2qEA+LgH/DjMOJia
        1bGFOSEuwVqTJb0hlSpI69QlacUiRNteixGf5ZKkVHbq8duvsOkAY3jeWzTslhCh8zJ6MDKcFzlj
        Z6eB8DQ/ifG8pcl4y3RbO7Odjpfhqs1Zo59cUMzzhnmzFSY8k9o9+tYXzZPZrlfCfW5U4e4ETBe1
        ez1v2ztFLnlfmX/dW8FS0HIVHlftSbxz87a4rsyZZgsyZ9vTLUnGDoc6ks5Ixb9+b+U+Ef7q61Yy
        b8GbjFkI+ZqXxLmZVTCWwkeCZFemBJ/Eupvf5tMHUitPFmiHqhvAN0Lo9orLCN4J+fytd3U71gt6
        QHXQcXhkFayM/ZLs171dzwcjmGEnugeszFafzDU9V2hHdMac+jkmrYhVB2WCkNJ8O0hJv+kNG85X
        c4MVC3RL+tRTD+ftqmWB0Al8TMqtDspWlJlDn2bNbpv3DC20LdUehzzgyHYE9FsfhkptjdWt6yGD
        zqJl/wwDDpc1hpaXGCO2EXLWUAdDbwwicwNNSDg740ZdePs3O6P6kY+SuevhbJ4VPGvGAzF7niuU
        R4dkkJXyktDMFUVoeYWJf2Fb/vC2Xqru98f373g5/ezeEhqbnGHxVIUBa/LlETI181mg1GYuCXfR
        R/4UbRi+Jwc0mdL1iYzT3sS8G17WEH+iEZT73cWjtzl0f/utoarBtvejGXzryQFybwdC9seQj2fV
        EWC7OlfM3IY6ooKWHdHdSCLciq2F+myoi189/rbz1d3WJW2im4pCErTWJO3vJrLqqSXOdf9JRvWj
        q/C2vYIZ0drs+szzehiE7Yr4evFJ5t1y7tVPLUrktFk/OXvcqx4+zoISk7E+6S4fzVa7wtbJ2n/U
        1jwPMkbLc7rA9+dNzimaFk+0iwqHEKVaWn3x7Br43g9yCKhTT6ez9YTyph7IhvUKYguN6KqXFvHP
        r5L+mXYS3D/1QNblXCacBJUJUjQVzM3vBeq3fHbhiSvt9/z4fNK6UR03L4uQQRDr4T0WM8qmbUzF
        rlXR3HhUR/l6KvFw2Ib5nE56pAlwK4kr+7q1cqNLgaKm0Oivfjgx9z26SKsZr3koWXNXnkf0vR68
        grNgUfVYu6AJpCC21A7BMBjXEDF2fBBvc2r5KGjXAr3v+pME02RaHB1vItwb7YbnoN/Ug5EbPno/
        niJW3cjruG+9MKxcnBJzSR41jUtPBdkpMixw75pQvljtwdn0b7LxqFXPTKR70F7piZhiO+bzHMcm
        lPp8IF65tdEgtjsR2BN7JLw+NwHf0yn+6R8d004Khk01Y6RbzoUQvCvyQUURhmRWYqbflA1nEpuP
        yOmyFcPm2AbzpOY9zJdMJ2Qa92g+rKTs188sfLU06XleXVAbOzKxk7HjfaMPDSi9oLBwhTH68QHo
        YSiTjfsorbGQoQD7HnoEm8GeD5+aR+jwfmLyvd8Be9zbHm3SbU3cN0/zaby5EuxE/8DcxaPtZu7Z
        FIhZ9eTHl5xLM0YdRjXWnG1mdZfP0lYrX7NYseSGxQ9JYkOz33HmG+apo5JihOpPj1VOUzRPBwW0
        22B9iHe6netR/bgybI0iozIZ191cBGYMz9Mno0oTary/lgKGSyS3WIBc4rR52BjkOnGIrYddx7Zr
        eMPYnBmmT07Rz39VL3j5bL1etwF/vVIbamHfkkDtH3w+iu8QBHtrEVOgq3r8uGUKMmkk4opAA34t
        2YwkTziSkF1oPayNMYUqW9vMgUubzNK11tXLZZ9jkcohH4t93KMu2qtkK/cm53qxD6EwBGD6irzr
        MZPk+fedLngx1IwPvazWF7Sn4qsz+Gi6kwS7wh0x+izuqC/PwlF9vfYzc92KIy73WQZaWYoMP4le
        T4NfVdqshD3me7TnfAuTCffCPZFwxe1g2dmaD/vbeP7ygdmtHP9lo7dz4SR0omc+DomSwV3ufRLI
        5xPnkhlEsDxnC7I9qkLQc28jgpJmHTHv8pXPnc2fGnWqiapLPAc0VS0JhMlgRA8Pr5qvnoOJdoU/
        YtU6rOsR2dcU7NjoyWaa3smkj4rwq2e2/vp1v977InTWdcZau3ugqRSOJXoZJ0y8m8QCOjqLGK2j
        HaXP6SJ/+/0QIgvsGq/Gt825Z8kCJL0y4BqmktNsKI9IZ2iDH9HHr+mvX1dlIf3tD16elTcKbQth
        fnFvfPjx0M9fnFc61ryM+iNqr9WHLtCyDvgqwCLKT1hiBqE94rVeyoCk3YeZ7jJIZlj1gtrr8u7L
        43ousm5QEZO8gemfVLW++0uAl5+G/fiWizyO0EK4bPHk4ATNZD+qf/mi8LSlxbJY/7d+W52749P1
        xI4/3qAqXJqAc0nFylrf+1T8pBxNm0oNUfPWC2KeItda8aGRtZE34Zc/zGDcnOUQos17x46Wklnf
        PADw2C99LBuHN+rh8RZAVY+AkVKbyarJl3vo5ySgg4kNa7yc1BbIu4qIfoi6nJ6LoYUwQhZVVqMQ
        TBe+rmBD1g7bFIsD4qv9xUXf/mFYoDYa5Tov4VsvxA7Ox4QvyE2AwCAd0dn1nc8r0xbBNdAJPxfJ
        sx5iYSGBuN9lWPC0QzA1NHFg5YYpsWr05IN1umNUt68FRp+sr6eT9jhCKL5iElQTQaN/dCrQ3PhI
        MPeuOUeTZKrN5CNmao99MH/98S8fWrvSD/jhUQuglZVI1nm8sQZPHQtN0dfzXx4cpgMqwTiYAx1f
        3duaIAlHkEcfU27jCPFSyDP111/bUa26iet+D7UyX7Cc3wvOjgT5gKl0wZXomBYfLnIMydiemRm9
        go4XBs/QrtFkOpsjoNYu6xB9vMRi68NW5n0er2MIosxljhsxxHns9GoYKRadBXqquVmvJPj2A7Fw
        LAefJ9kLP15gtl3frae8/ETo+38pLB5NPvPFYg+5f6Bs0w06n8/W5gJpMa+JqyV1x6O6jNE+NhLm
        WIpqzdUxiUG51y5dnKU1HzFxWoRUOSH+tx7nR6s8IdgLRzzdUfv1NxVDmIQp8apbx6cyUjB88y7z
        5rfRiZ9Fbv7yA+W/fkXbq4hKXCyY/vPHgFr4l/eJq35uvP/lZ/1s63T88vrIukGGw1VbMKO2t/XY
        xMIFfvpsvN454tL1foHb+5qzwF/p6Ju3XNBhJriThSEYyzpoAaSlzjZOOdXdQhlFYO14YrfrsuCT
        N9UuzPZ6Q9xT9UBcxPJ3XhD25Ku33XJ7vwI0ZNaJw9gz+F0PWr2OZywOvmiN70BqYJs9pS+PVgn1
        ps8TGXIPxDiVQ8BELJewslofi3rY1Uy1I6oVwvQiRjk/u8/B2B5/PE5yPdcsZu6ooPD29cTPWAm/
        eV+pQHaHhGHpRHIOidfCl2+Z+ejWQZ+Uax2qQykR86rYSDIby//55c8/0Vz2tgNeeonxEtVdPS3G
        MQViiwZxzaLPmRyJIVwfbkTn6sotrtRrG1T94zHHHKWOohulQFuik29+7ebLaZjhfVI9+prVjo/c
        IyJqZedC1plX13PmgqQuT5sWS9FTyKd08iQl0z+IWHsk8vlJkky5XlZHZtn8GbBd34l/rz8oFqQb
        9485g3O9IiS8ydSiU9i0igrv3XZU10XQO1cnhfOu+e6PI97JfZaqCTg+Xubxy+LbdFsqy9O6xbfh
        fMv/8tllThrcHjsnnw/+Zg+pJjssGd+dNRweL+eXJ/DYOTEaBacS4eAVWyxc5cAassU8gp3jhrgD
        syzu39YZbE6pzr58FXA9tI5wq5UtnuEydl/+NZFnFhQL4trNZzNbSmiK6ZaR0aqD2TsGmfrZrnbE
        1cN1MojQUvVNHoTQRZxYUxODim4QOuQS7QfEhssYI08PzsTg066bfvn+L5/rI6/Zz99//R2+nNL6
        8TcE0iFiDqe3gH7qiYLeHRW2XdtSR4fHMoMFO4bM+OaH/rK+z9Cj/EW2n3785oWgQbtzUxG7a1U+
        OcazBdn21swvrk43L9pFr3bRUf3qfcPH4WlGYHRLEZfLO+Hcn44RKLcIMzMypfzTfwoVfedXVNvm
        YfDtl6fq77MFC2b10c0d1lVoUvlKzMg8JmN3Oh9BlW2Lff0i4YfL8Q3nekmwHJs94vtLmWq/fprm
        N7WGztZc5bc+eKlJMi9SKqFimw4kpLKeSxZ4Kazjl8UCp+wSxpxlD73iUmbiQ5P3umUXWmrIAgkX
        OrHmX72/aHggTrw/WbOIVRuMzxNR5JRBPgvOFdAyvl/wyl9tginLd6b2ux+2XRvBcptmKaSa6rAw
        Sw/B6C7Dt+Lp3vk778PB+6iNrVYvQ8z8RXxO2KVUKjXWJPebj0prVNF5RmyrEPLN66jzprcOiiIa
        xDJ04a++qriNMLmZ5SPoH04sgAVOzZz3fZ8zU8oK1WuHgLl6+EimT40idDj5MjE20NfzllilcjJS
        i+7Dg5Hw73wEYvrp/p0/NtXYAk/GGyHPUEyGo/iZ4fVpN1T0umvH9HFw0I8vouAMNSdBa6IrjXtC
        zHId8CmsHTiuMxWfSH7IeSkkKfryM7Hxy02+PNbCt/6pKkGLmBtdLmiOBIKF+gFo7G2hB1MCAWt3
        NcrF/rR5wrMi228e8TuuvtULKgg12fra0IQXgUvRV99YcnEXaHwVZwz3SXwRw+GfYPrOd5Cdhw1J
        u/Ut/85XY4jrrcocsfUDNBixrUm4avDi/naCeZumImih2DBvVN98FNdKC6rHHxTyu1xTc6dGQJ1y
        YmFv2MFy4dwqdBjcC1nX9ivpB+NdgRzob+KtbUAD5lREc+Z0xF/yV843588IX71i+JXaweiwU/x3
        3vU9nrV8F1sZbYzsQpWXlQWTfkhmUISM4La5Y2tykkWJeq0HjHAc1Oz1Sp3fPJKisJn5XL17X/XN
        xQfzakuDHpLIQVNI33RqpkXNz4diRJfisac/PhsX91UM86G6Yu1xQMH0LrYq0izq0tkMac1XgSOh
        K9JOLEBLLxnLPgJo6iKi6OtX/WdXC5DOlwMWVuWMGumJCng2VcVItvBynrkoRtMpytnV17/z4mel
        K96qSdh+YVT19OMHLux3xE2dfcLLzcmEV4d0vCy3QdBv215CCjgqs0J9Z4mH5DbKc4k4/fqBRY0h
        uvzlJX/JNzkVtHivbj6twowdflojvt8q2N/mM9tmwzofHX9ZoYMUK8QhhdHRGJUOxOPaJMYRnfh4
        uDwctSC9yRzmR4g/yFmEZVxfiOfgV94Wz/KiLfNOxmos36wp81xX/fIe0xsxQOO1FELI5XdIsnsC
        1rzaX3uIC7qhstet8mEjRClcHZN89TupV5tNpMKw3OQsYJsimbcwP0EOzDdbL1QjUIF+Mvi+/yFB
        OrzrUZ3DAu5StiT+w7Hyv/NaNRVMuvz679iVexUiplLmCaEZiOXmUYA39iNxzPCd02C1xqBVksdI
        1w85bygO4XOLX3gpHs5omr3UBFxnHfOp8bKaNbub6Jsv2enxjuvZWNz3f+cxG7RkHWPdB8Nznd2J
        rocTmmu9LdQ+8XziOksDSd4tpeozmlYEM7/JOVF2tnZ4N5iFnpNzziLL1NZRQsl3Pt193ydlcAqn
        9C8P0nirmFD5C4t5xrpGbCqQAJE+U+ZIacVH75jGKszRTHQZspoPcthDSl5vLAm+ZVHniSX0PT+z
        z0Swvtd3QRu/lInubZZdv2IrE/bwWuM6bBb56Dxz+m8/tMt3Pgup3CCBjku2O+vPZCZB94Rv3mX4
        JFr1VNmBgLBKZroA7cbZpZwq2Mm0ZmvxcOZffX1CJz9OjKRDlc/9duWrYqzfmb9jqO43vWf/9b9T
        qPuIgy7PEA2vM00uw8Gii0P0Bn9Tr4i3zefvPP2tImRdzyyUWr1bjd3tAhNdV8xu76t8ZCIPoW4f
        C2KHh3vCJDOIwYguNwxetwvmT9ZnMIz1lgTxxwjG2JYb+OrXl6+Hbt7LrgvhMPrEj/cj7+TMK+Hy
        lDHxU21bjy8T6fCd91DNmVn+5YM3wMc/MMNNqmS2PmMFoPU1M/fnjs9xaaia9OQmsY6foe6bizXK
        0DQblj40E/Wr4BqpFf3EZHuYsq4T70aqlvz508Mm6L1bIqqWwjFG1fZe00JWjpAsw9ffvDNLiocB
        vwQFv8n4qOddP2RwUbwNM/R7FUw//3LC/wEAAP//nHxJk4LAtub+/oobd0u/AJkyeTsmmc1UQMSI
        jg5wQFFEhkwgI95/f4F1u6MXveplVYVSJOd80zm6F4m8+p11fhEATfnukXIXqTULuQfhshx9bPzm
        X1wfvaBMiERG0bxbfZMfcxgaux7xap/3U9Cein/PH8HGsv74b8VbcljxZljxBvDcbYcjAX4YZeIS
        aZ9o4hH/sPZgPhzipybkm5bi431K5nfexUDd7k7UgnMFWlZ2N+gdWxNl6/yGcMLWhO9mX9GIf7+S
        ccemF0yvV5sArr8B5gQfR/3lGy8BCsl0KB45fI+BRbfz3S6n76Hn4Fn3dWp7T9WaVj7QVv9G0b6Z
        wfg5BilonRv75cNsHACdfvkFmiNp+ndenUtmTaMxcGuaR+W/81MUyzKjrXKKfv6LcA1DYKCaOEEh
        aygB7hexyW+/AZxaXvvp3XK+dD6El53PUZMFp7CVKG+qqaxL1Dk+v+G0SF4LVn+Gw90olrNo5p72
        qe6YnALpE7IbfV7ATw9mIjxY0spnyoFvHOzwkgD6Q317AkyPp1+99uyl3z31g+2UaM3t1g+QvJ7w
        d14Lvn3Y9MjaAPr2+MHu/qQnmx4FF+XnB+1Cy9h84P0GmCLH0ZDdGPvNB4F4PA7U/jhRP6/nB8E+
        e1C/O0ShFPppA3dcskVyqN3KKY8eHeyibbLOz69g5HLVg8kdm0R9Xu71er4Evs3mQN1WfNVzv7z+
        nR8Ga1769/p//bYC/ut//H9sFGz+3xsFtRJ8kJL7SUnOgaLDelwA3QKKw6Uyhgs0tG9JrR634Qz1
        oALjQS4pfilPa3qNwgTj97sgy/Xk1m2kVDHcRukNu5Pg1SLe+i9Qc+YNe13FgdmKuwbKGoLYtc9x
        2LD7q4G8ygIi2efYmlM4eDDBTU+9k/9lc2jBAr4l18ChevYAkz+HRlMlcCGbu2qGEx+gQlW2KaFu
        O2G2RN+tDJP0UuPIOlTWfJK9AQ7At7D13vWMDkUUwOScFWRzi7fWhAUpUAs139Jy+KrJsn0XAhTS
        tML7r9ony0YIEFAC9Kbm/AQW1ccpguN5+RIZpk3JgqY1weE8WQieZZpMJRkDIIMXj5GWPcHQwmkB
        YIzPFIkiKrvKs/fAzPSJiK5lWhvt4+cQpSIlBCc0XHoQ5yB8zTF19w+r31zVTQNf9jOhLo3Ccv62
        nQcel9eHovS9BctJLFO4AfUTSYYmll3nljJ85/cSu4ejGRLurhaAHXcCYTU/smVj+DIcokRFfLW1
        esLF6gRLbttRw9FEa5qErgDr3wnlq4M1hdvHBHh9VqjrSUG4PL/fJ1RC8bk+ryUcNcNbsyWQ4O29
        eoJpMatYK0xzxt6NWeVEzi9O+2TnDY5EF/TfY93bcuSrA6lttQFz61mOOiuBip1Cl8AYp9SGj3lV
        9Ny7s+YgO7ZwnCqDnnCyrZdFHjvweNzuSB6gWi/X8yeFwXYw8P2y8UvCK0qnrvWDUfp+s3bbUhm2
        mQqovXv5vcS9XgIsavmArzc571ks71WojMQkU1C+GFmWnQBZfiWEK0u9JoeliuHL8fZ4Zxi4nBdd
        lWFSwBPSbrYati1VPAAk06PW2Fvsy4rFg2CTh0iS9i/r89Q3MegUjuLw0PZgkuTrDRbhE+JQ0kZr
        tpXHDdZxuUeSEus9Q0D0IMvvBKPk5ALxy2AOTdvfY/uAamuirkVANfFnjMz5Vs7NuGsgEgmmbu/F
        9dxUWwj7PXExLs5jz8QBIOBbxxKbWjf0tLQ6Hc7P3iGDaZlJFTHvAk2l2qHPQ7yECzpYJv/ADUeU
        vI7r6Vy4T/gGnIwA/3FqaahqCIXhdiDSWg+CK44E6Cj74CBqn/V8NY43yPNfg4byHILFlqADi/z5
        JvbGbcHg4EaAztUaEOh2t342+dCGSgJT7O2sFFD8TWUgmtYJPTaFHw5SnC7wJu4VqmfPez18cOFA
        8W7yOELB1WLeYLQw2BID3ws2gGlTS6J6lKoTvkXuyKar37XwkjsQqbq6L2efZgPc3e8z0djNDKch
        JxOIqO7gU0eSchme5/SvP9T2iutecpYKVHsqUudyYf3HW74XcE7nNxmZbTLxddJjyPnCmRqtfWaL
        q8g3NS6lngAl7MPl9GQqhCodiazcwmSOJFWFuXkr0GTOfcLosRpUJo8Huj7PZG7ULYLB7oVp4Dx3
        ZQcP8AXbqu1wvimW/vvZDQQmr8FB+7uQWUQCLYJHvvOoU6JNT+VKWcBB14/U5+xNOXNIT2Fmqhbp
        pXoHltqqBLCNhQQf02gKFySFBArD5UCR6rT1FPTdDfYJZxBe3p/CZQ4RBCeZ9Ih1G4/NjJxf8LpN
        PYrPPEroRWorcB5ARiOOX9jkc+EAvcl9UKsxuoSeHs4TuuE6LrrODljaN9eoYl6H1Hqdd8m0+J6t
        WqEXkOkVk3ICJC9U399gHAi8nrCnrsXATc0PRm2mWpMelw1stS6mRty35fIo6wGe5cREMy6J9VdP
        4CCvjucm1MPk+bIqbqYTxoeHWNIcjXuVPx1G6rxJEM4POzfBw4ggmlz+a00R02/gqHgpTekmDqfk
        qt6ApZsmjVRQWHRMn5VKBYipb2ZmPQveXoa7+3UmMl99+mkIPwGU8HO7nv+xZAWub9DvnAVbjzpO
        FnDKELzVOo+942MA9OkeHYg4USdgrxNGtc65Qd42GPU2wszGSFpU7fY4m2QyT5FFt912Dx/2TSQP
        tvSM2X6D1LXfCX94iAnZfMzX7/VIenZF/9XUSoCn6qXi+0k7sPV8cxi/PwWOLmkfEtsGE3Q640g0
        Y7Njs9yRFjTb4YPDc/xORp/eCLCv9yNGjiH3Q8qdK+gp7wG7zmZXd2dNyeDDFBUELp6eTF732qsf
        +XCkfrWLrPl7NIkGpirF+Tl0Srat3zew384VjZ6Zm0yXr1VB0epTotz49fnJfQy+bm7gq2uF9TTk
        zQKO49RSA29Wh9qqGRA6ISXTQ1z6sYIz1AzJ//zwlQ2f46yCaWfrNKhDGUyf51zBDjTD+nzcminp
        lMHvwm9IZWgDI0u09UDG3UVsfIcMLFl7rADqsxrjncUlsxqFHMDyLGELtkrdFga6gEeTZjTaJNdw
        0HOQg+IY99Q6WVc2f9KoAs0r2+GQKULZHMujCuv4vCdiPzXJeANsgtExztEzvjLGkm3+gvbgAGzV
        b9QvzlaB0J44hk6iaIP37qaIoICmifJT/bVWfbX/4fVf/U3HftqrTScY+HKLUT388DppMoswLrqy
        uXQkG5wPj54amm32QmnarWaMZ5l6F8MoF3GjF5orVTZ10d0NvzS+cgC5koP9bqwSym9D+MMvapY0
        SN7qe2tCNS15oh7EKpmjOXHg6YgR0l4BTlg1nBoY7bwGcSn/tki7iAiac3FBXBo29Xinaqrug+9p
        rY8hZK9jrEKZExW81kM5lw7vAH2AO7xb+Xn5cHwLbCEaaTklCfhOby0HlGUxIsfzq5yuWRfDVvBc
        GiphaA0329e1sdMjfNr1bT1FQ9WqdCZHIuofp/7OzuUJXqaa0ajTKzbDnRnABxUfdPvwKmvJzFQA
        LWI+RtDbhlSwOQ+07VSiubUVwCrlhKBTJR+Kk/sZTCV4LeB+kD2kPVIhmT6XMwG+L+G/ehmFUk5/
        54e9uJ8B8x0PAlOME2zvOI+xyrnJ8P3EAZm5WE822sfIoZ5dznR7vTX1MrSKB8a+8/FPn3W5rwrA
        sRKOfNDWZRJ325nQjfOOItoIFtv7gq4+7IuIJOudJCMrlgAASfeQ8rk24TB5hgwtbbcQ5fYmYI7d
        zw1Kvn7Anh1q/YALXv3DI2n7oYx0afKEgS4GiK76SsyvRfz3M7+zuHK+X2IZdJzQ4P2yPPsZybOj
        dfUFYBcpTcJKLfAgftca/vHZIpa+BzE0Zhw0BmPDNXvGGgIvB2N5f7KGYy/HEOz3MxI1/8RIbbUi
        FObMRxvhvrG6WvPgn57YzeLW2kCny+CliggSAoJ65s71HtK7KGD9upwBy1T8Ujv//sVb4X4MZ03w
        dXDEvklaER1Cdm5fTxhOHke+yQGwGd1lE6hcuUebfN+Csau9J+y95ogmwydJv6/BHtIeVdjORN1i
        RRbuwZEzQ/IRL++a8LtDALenIaAJr1+SuYN+A378MJHcKll58i/QiiaXnvbguer3JAVyfUfUfVfH
        nsYv5Qaos3Woean9UPC5kMDm4CSoOUZKwvo10Ri4XKMua7qe2ZSoQBnsLd0ullKyq10hDRy9lqYb
        4JZr/3Qg3ZQpks9hkxCRtA0I4Fmjge7MgHqxmQNLHDnE4ZGUkx9aBTSqnhFOy0y2aaodhHfdbLHl
        ZmbJ9E+eg3h7kiieIq6kP73qnvYP6tk8tAbTzTNIm+yLg6BprIVX5hb+9JJiPO2wi8TdE4pd0WMn
        zQuwFH3uwCU0C4p/+i3ZqwL48qO6+ps3oO7WXED5+YjU9N+wp/TZpjAx0BdjD9/ZsPYbvKQbE696
        o5ye/F0A/MO4UG95nfpp9aOarQ0y4Z/WJ+z3GehU5YQQUYW3EU7BYu5BNtMZ8eeQArKVlz2oFDf4
        qxd2v985ELZWhPWPPZVDfIF7kJyGK43hBYFpdLZPeFLTlrohHthQ6zcHkuX9JRKN+nK+0yXTTh5+
        kyst/IQqqZzCVc8gJVUUawGvqwC3l5dBvYx2PXMOdgQTU2joluO/5Rx1YA8v50HB5vX0qckI80YV
        u7wnctzPbHn6wQ3q5BXQez5PfV98O6L64T1C3Oo3Z5h8IdAmu6dmZMjJ62b7Jjw4lb+eHw/YkLwy
        KLlFSkMNW2wxTL6A4bf3ceB4yGoPrTaAvW8VNGg2Sznv3ucOrv2Epi4NajKa00Urdp5Cd998F07H
        29SBVf9i/B6rno1j/QL1K57I/ZcXvFGpwqS2fWzM2T6ZIqXdg9bqjT+/vPrNBZbdZqImNGm/hBcn
        Bn7y4rCthEa9OOdzAW+7dWlzSxAQuAyLQDg4DRHMbsPG2P1cfvVFJu/6SMYrujq/+//hAVuC1iXg
        /vZMwiGFsp9egdUHd9Q84MyaIl33oHNmy4o/EhjNdyiAtC17pNHiWzJNQja09+MebcARg0UyPQeu
        /g9t8s6zGCnNVI2ee4MeBjhbkxIBD6z4gi2aVOxDwKP48RUOyHboWTGZFbxMlwNRuFtat6k0D0AF
        fobNyMgThjxNAK7iGTQdr7hc7PkSq77N59i/n3flmi9MULhfWvQ9jlJP6sDrIApdHfvfbZ2QT85x
        sOykCQc1LyXz7rAgQDZSTdQ74Op+l0oxPMaFRuo1H/neL7GqPWrnQmaDyuXYknCCUdKWOITlu5wu
        F0sF4aWvyBg+rH4+XsMYsCe+kgcwG2u04u4F+21aoj61Pz1b9Tacn18HCVPSJZODiQDjkYZEDZom
        pOzKI/hh5xsOD2jo2d66IHhJdlu8+peE2bCpgLRTPbwdyksymY3y53cI4D9NPy9pnEGheM044s+L
        NceEpPB47CTqXhAfvkiyDSB77q5IZrkYsu+NNkB8rxs/bncPWTzPjroNhgc10tEGNO4Pe3gKr3dq
        6C5IiKCHA3RdkyM//J/Bvc5BfQm4v3r/QueZ/viHBtawlFRBeQXl3H3SYKntnn37DwLAnyCSyDKE
        06qfgQrn5M/PU5fr1J+/wvpDXGr2qG4BoK9h+OP3mRWqp8bSTUUAN7hk8e3cAlVSLmRh6pURcd1o
        FZ+xSvUu7fplfqMIrHkORngkyVIB+wUJPRJMeXWfMH4ebnD1L/i+9ufyCfkUsCMWsFntnn07cJ8L
        6M40Ipus7eulS8snSD6cTr24P7CpQMiGzf1ypBftdGSU76MONkVzo+aTf/WTwasxYGjk6ZYLA8ZG
        t1PB/bxk2OknJ2FXu0WwlqruLx+USs0M4C0zMmorfGWNpD/p0KV5TPHPr7jatoIl53bkkNGHRaHl
        vmCecwjb0/jt6WujQODbWk7x89qUE/jIDXyPx5gacmeVs73zONh8GhcNu4yrCWuiAjbmbSQy73jJ
        5qpqLyjy+yc2WlkuB7B+goW0G5VA66CHwtq/IuYPPY2eU1OzR5I9oUrCEYlG+Q5nH2bpj98o3id5
        T4Om1eHPL+6WzuqZrxix5jfCT28/yu6nt59TPVF3Etp6ku9prqmv7xNt7tWTtel8kMG5tETSCbxe
        siScCrj6HeoVZzsU9oi7wA2pFaJ+8KH/y+vqtzruOOi9w4U1dqHRgtOoPul6PWqCYUJwNUUEj0JW
        T8l+EcDvejzqy3LWxkekrXkImg2aJ5NSFQ0InXDAu0eaJqQrWAaHmFEiiPTNuoxsUqhYw0wd92ok
        4+ykFXzc0AsH87FhIzmcFmgUIUckcxYYMS9cC/hPvSfTqg+X3VSrcAMeT7KI2bkn9gEUMHlcIqo/
        TCEcO/N7AVdrCGk8P0ur43fnAKrpmcfbJJ/L5aDozu98Ue9lm/K74hk0t2xHBrmWyuF6NR1YnxKX
        sHB69FQX9joMz2VDBOMih9OF1qm28iNZQo0vGWdvF8iFt4zqDzGup4O5MWHuZgckoLy1Vr0aKEcl
        SKm9L1798k2OGZhDhWCPptdy0pXiBjkvuuCr8x1KlqluA0/J54Da7BiWypcaUGu1NsZo5E7hVCDH
        Bh14DXSbRpO15Nd4D+Y30InalIk1//JDxYH9n39kvzygfB0h/eH5vB0rE679iri53dfz6ebfwKqf
        sJfcp5KNaVfBW3o+U1MVDv1k6tMT5Adbp+EBRfVkskkEh/NiEaUbq3I+WpGpGjUYsetdA7ac7XME
        Wv3sYk+59SVxnSmAMw5b6nejnkj7a3kBt9rk8XaxziVLtvsGJgQd0PM6O0x6X2QZ8p/HnjzkvW3N
        24+OtB8/sAtNAKMX/Qm6ChvYDlKa9Em8j4Dd3TjCmvJbE5tLZAiSZ0/Aae+xofWnG5zkXkC+thfB
        0vKa+MvniCTLRrn6zRdADyGnmYOtWoiUNlbWevzpW7YMx8MAWmgecSjPPRs97y6C8dZVhFOrD/gm
        YOSUxZ2qX//WGw552R9eCJZ9tGbhmXvQQoVNNqXZgTb+AlkN5wVQU8zONRMk7gbj7VGigTQ/y0mK
        LxN4+UWAd80zDj+b5SKA+O6+Ef+6P8EwhB/vx6fU2CfbfmaGYoPmfjti21f3VsMNqg4bN3th960K
        bDYOrwL+6sdWeD3sh2RIoaXwMnZl2Ug236eZavlV3JIlVXDNfEeHoCtPPt213I5JB019wnZ5BtT2
        sp7Rn/5Y8zbqrXy4Wf0OzM1LgaPXUlvL0+w6+PalzV89Vak9FyAJiED96+lgsQvnpeot6Ls/vvvp
        M9hp6IuYFX/r/vf+K99SQwcKGBGfyzAwLy4OnVQGy4NaATzm8kzDNf9ddv7bhPqhedJdrwcWlfRi
        gH4/UGwQVaj7t6l0v3kQDla9LlZHS4Uod74EkINUzur25UHlFCGcqSMMJ77GF5jfb+bPj5eEd+ZM
        Oz70mlyjo1lK+icvoCRt+D88/JtHrf5znX9k4Tjdjw0ERM3oL2+cI3t5wV8etvZ7wjay0P7y5zUf
        lUISXtAe7B85pU7z9Goxh8wD+y2ryAvlbThc98EN7uC1wP6qz2b5Sk1oKZqMXamrSzqqPVJsi34o
        ih47i50e6AlhEJvUemhz/7S5UpVU4o9kDJItmPRFbX9+h3CTEFvT83z31BD3EpHFdkqG1yeFP79J
        7b3+TiYucwW4DcgDh/uChCs+POGaD2PX0MSEbuvxAgwp/GBjCRCbtY9fwJ++E9g2KjcCmoYfXtJQ
        Pg39VLNbDo6prq7nfyrnX16kO5GIDRF9enYiKIC//OzHp+MntSuoQWGiDv9Q+0WtT+0vfybn43iq
        50cty9CpHw8Edq8knO4HvYXrfAgBlM/955Fkf/xCTbw710yvpgaevN2bbvO9x4TmwTLt+Gyrtb9D
        Rn95mmBedYr5ag7p6vdA46YvjILtnMxzA14AdOadhiprw2GPuBtc88iVr1K2/v+eurhLReaFGxM2
        JEMGfvllFHNlzXRhb2oBRhEN+03ZTy2dA23tdyQfwRDOURgX4Hc/89G1LeGnl9a8YN0IReAvn9Dm
        oaWW7nhJY4qaDNPNOSVxbKX9fOLaJzwYXYyETKysNb/PoFPXDzRpPV9T7vRG0L5vY/xXL3I1T1pg
        OzY5rOdNe2U0IYXiFetvZ2BzAt4QXicFIqnN9v0wvkkB893riY3jw/o3n466Cdb87xOOVhOYIMrU
        43p9Pdy8E5SKKCIaYrygsvGY9ReoP8Qt4XlnZiSibwJRbn+pdZJgMoXxo9CSS5NSi2zrsGkXMYJd
        tTMQSfn3bx6ZQ6P6MowA2fZLPfmZ+ps3r/NesPDJxEF3XI7Y4502obsHJ4NjqHgYn7QZrPltqplX
        lSPLk3+tfK2Z8BZ8O+xY7AMW8yvuITC+IbWNSx6O2dSRv36yA4LqMbRg/uNvJDbwA9hmSQXt3Cdn
        IjyO11/95yr9NjV2346YrH5H/vljbIWT0W9+85f+7vjYOPlVUhXIcYDpGy7Z0I9Wjw9tCuBRep7Q
        HB++1nJ/bDPoPvT9il8PazbflqCu/IJ/fDMv+qL+8Jys+s1aInFbgSUrHIreDs++b8XV4TDUV8K1
        WRGuftOGF/A644DcQ/DrNyAWoUc4+HyV32k4RnCd91IEyLueLpdQhZJvHv7m9cQdJhH88suAbKN+
        ViOL+98bBf/45z//5+87C5r2enuviwHjbR7/4/+sCvxHcS3+QxDEvy82IENR3f71n//eQPjXt2+b
        7/i/xvZ1+wz/+s9/in+rBv8a27F4/1+//sd6of/6x38DAAD//wMAjOe+STBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c08dd8fa56-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=2kFuauO.O5ci8J5HVplzLXEY8LVHZZkvJOJ8Hb0yhCc-**********-*******-vqdyu9angaN80zBy2in5hm3j_Z4zO9c3m2hurmmbgsc9vnMJnR4h.OG9DGBqHcLdFMeMdqCjpjuar4uqPBkjIQ;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=eC31E2qAfREFYM4a4l91ZiSzMkL5BHBdzxTqvrIRJWs-**********392-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '18'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9995'
      x-ratelimit-remaining-tokens:
      - '9999928'
      x-ratelimit-reset-requests:
      - 24ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_142cf77ae8bf0fb2ea86ea3c36b88aac
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [2822]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkka
        Ye+1vrX57//48+efd14Xl+Gff/3551H1wz//+f3tmg3ZP//681//8efPnz///fv8fyuLZ15cr9Wr
        /C3/baxe12L6519/xP/95f8W/evPP0oPCr3k97QbBzlxYSnXOts24aqbBvncQOQpPvMf7dTNl5Lb
        cN4uW2ZfzUcyGlIpa8w7GHjyLms0WlwXYM+YhmczxLV4Dm8ygjmeCV7EPJjU22xqUYxtYhy2t2Aq
        l5kASXq4MWdtapy+DvqonYgzMGd1fyZcV9RU/Qjpm5hS6gXjUayP8Nj4N6o81GM9VceLqB6uiwUV
        vPMT9fvkUyFbPT7wbE41Hxv90cBCxyEeA2GH5qOj+GDHVo+lVON8Wu+9GAWLa8/W5bazuMTUPciP
        Z07IfGgttstiQR3ZsmR6/+ytPi3UErnibOHV3baTcTDKCmYF90S/KS8+jhHpkWCAT65k4MF4csQY
        hOVDo8rxKFlU6IwL+G9tS9VDZXKxeNYNeDZ7UVhvTM5eY/KGcXi+qCZvtt10Ll4Y0LnQmIEfn/zd
        rd0Lip0cYy0b1gn31PqJCipZVNmgszWnh9aGK416EqYODUYcjIXsbUygaFYf9Wg2OEKP8zkl1q5s
        Lep2YghGxVJmaO8yGadwqpB6fA946vL6ez/mPfAbjihfn/b5uBHOAmwf7ocFZHFNOompR2AqQ8Ss
        rkkwmU1IYXhlI9mu7WPN7+eXqm2NS0anzemUj4swDVElQYgPx+MlnwLvXqDHxr2Ra7t78PF1CGR4
        XBdPOg7BPZmK/c4FeZvcmblYoYCag0Ph+zzwaldafOXIRxfAN/fEeaVRPe7qRgBzu/OJpyz7bvJV
        2sNnGYfMXhhmJ639OUWdaHlka+J7MGaeQcENDE67o91xengMJZQfc8a7fGXWg3DXelWKeIGV+X3v
        eLl0bOgKR6dKUuo1X57ARloHD2JVs5WPnjrrqHDFG1nzqcvfa0MR/p4PB92ARsfXKhAC50mwtZeT
        uVkdbGQqUYIFV5y6QRZkF143SyYhUDdYJZ9AhlPIUyxXLOK83NxMtB7cJcFXuQvmt0l92GnRxNyu
        Fet5s/RKKLbZQMxL0AejOSQNetxpTLZd9smnWFiJ8MCmycySZcmYqlEPURW8SRgqVjA9Wu4vdBkb
        jJS3qub661OhWZtVEmLTqZe7uk6RcLcD4lBjyKdVkFO01KOaednQ1ZRtPF3Rm+uR6X6kBN0zvWYo
        3XsrRuaDH0yLUc7UOImOeO8v3WB6iO4FXsYBM3d5t/JxkHMXsjz28GxYgTUK2qEA+LgH/DjMOJia
        1bGFOSEuwVqTJb0hlSpI69QlacUiRNteixGf5ZKkVHbq8duvsOkAY3jeWzTslhCh8zJ6MDKcFzlj
        Z6eB8DQ/ifG8pcl4y3RbO7Odjpfhqs1Zo59cUMzzhnmzFSY8k9o9+tYXzZPZrlfCfW5U4e4ETBe1
        ez1v2ztFLnlfmX/dW8FS0HIVHlftSbxz87a4rsyZZgsyZ9vTLUnGDoc6ks5Ixb9+b+U+Ef7q61Yy
        b8GbjFkI+ZqXxLmZVTCWwkeCZFemBJ/Eupvf5tMHUitPFmiHqhvAN0Lo9orLCN4J+fytd3U71gt6
        QHXQcXhkFayM/ZLs171dzwcjmGEnugeszFafzDU9V2hHdMac+jkmrYhVB2WCkNJ8O0hJv+kNG85X
        c4MVC3RL+tRTD+ftqmWB0Al8TMqtDspWlJlDn2bNbpv3DC20LdUehzzgyHYE9FsfhkptjdWt6yGD
        zqJl/wwDDpc1hpaXGCO2EXLWUAdDbwwicwNNSDg740ZdePs3O6P6kY+SuevhbJ4VPGvGAzF7niuU
        R4dkkJXyktDMFUVoeYWJf2Fb/vC2Xqru98f373g5/ezeEhqbnGHxVIUBa/LlETI181mg1GYuCXfR
        R/4UbRi+Jwc0mdL1iYzT3sS8G17WEH+iEZT73cWjtzl0f/utoarBtvejGXzryQFybwdC9seQj2fV
        EWC7OlfM3IY6ooKWHdHdSCLciq2F+myoi189/rbz1d3WJW2im4pCErTWJO3vJrLqqSXOdf9JRvWj
        q/C2vYIZ0drs+szzehiE7Yr4evFJ5t1y7tVPLUrktFk/OXvcqx4+zoISk7E+6S4fzVa7wtbJ2n/U
        1jwPMkbLc7rA9+dNzimaFk+0iwqHEKVaWn3x7Br43g9yCKhTT6ez9YTyph7IhvUKYguN6KqXFvHP
        r5L+mXYS3D/1QNblXCacBJUJUjQVzM3vBeq3fHbhiSvt9/z4fNK6UR03L4uQQRDr4T0WM8qmbUzF
        rlXR3HhUR/l6KvFw2Ib5nE56pAlwK4kr+7q1cqNLgaKm0Oivfjgx9z26SKsZr3koWXNXnkf0vR68
        grNgUfVYu6AJpCC21A7BMBjXEDF2fBBvc2r5KGjXAr3v+pME02RaHB1vItwb7YbnoN/Ug5EbPno/
        niJW3cjruG+9MKxcnBJzSR41jUtPBdkpMixw75pQvljtwdn0b7LxqFXPTKR70F7piZhiO+bzHMcm
        lPp8IF65tdEgtjsR2BN7JLw+NwHf0yn+6R8d004Khk01Y6RbzoUQvCvyQUURhmRWYqbflA1nEpuP
        yOmyFcPm2AbzpOY9zJdMJ2Qa92g+rKTs188sfLU06XleXVAbOzKxk7HjfaMPDSi9oLBwhTH68QHo
        YSiTjfsorbGQoQD7HnoEm8GeD5+aR+jwfmLyvd8Be9zbHm3SbU3cN0/zaby5EuxE/8DcxaPtZu7Z
        FIhZ9eTHl5xLM0YdRjXWnG1mdZfP0lYrX7NYseSGxQ9JYkOz33HmG+apo5JihOpPj1VOUzRPBwW0
        22B9iHe6netR/bgybI0iozIZ191cBGYMz9Mno0oTary/lgKGSyS3WIBc4rR52BjkOnGIrYddx7Zr
        eMPYnBmmT07Rz39VL3j5bL1etwF/vVIbamHfkkDtH3w+iu8QBHtrEVOgq3r8uGUKMmkk4opAA34t
        2YwkTziSkF1oPayNMYUqW9vMgUubzNK11tXLZZ9jkcohH4t93KMu2qtkK/cm53qxD6EwBGD6irzr
        MZPk+fedLngx1IwPvazWF7Sn4qsz+Gi6kwS7wh0x+izuqC/PwlF9vfYzc92KIy73WQZaWYoMP4le
        T4NfVdqshD3me7TnfAuTCffCPZFwxe1g2dmaD/vbeP7ygdmtHP9lo7dz4SR0omc+DomSwV3ufRLI
        5xPnkhlEsDxnC7I9qkLQc28jgpJmHTHv8pXPnc2fGnWqiapLPAc0VS0JhMlgRA8Pr5qvnoOJdoU/
        YtU6rOsR2dcU7NjoyWaa3smkj4rwq2e2/vp1v977InTWdcZau3ugqRSOJXoZJ0y8m8QCOjqLGK2j
        HaXP6SJ/+/0QIgvsGq/Gt825Z8kCJL0y4BqmktNsKI9IZ2iDH9HHr+mvX1dlIf3tD16elTcKbQth
        fnFvfPjx0M9fnFc61ryM+iNqr9WHLtCyDvgqwCLKT1hiBqE94rVeyoCk3YeZ7jJIZlj1gtrr8u7L
        43ousm5QEZO8gemfVLW++0uAl5+G/fiWizyO0EK4bPHk4ATNZD+qf/mi8LSlxbJY/7d+W52749P1
        xI4/3qAqXJqAc0nFylrf+1T8pBxNm0oNUfPWC2KeItda8aGRtZE34Zc/zGDcnOUQos17x46Wklnf
        PADw2C99LBuHN+rh8RZAVY+AkVKbyarJl3vo5ySgg4kNa7yc1BbIu4qIfoi6nJ6LoYUwQhZVVqMQ
        TBe+rmBD1g7bFIsD4qv9xUXf/mFYoDYa5Tov4VsvxA7Ox4QvyE2AwCAd0dn1nc8r0xbBNdAJPxfJ
        sx5iYSGBuN9lWPC0QzA1NHFg5YYpsWr05IN1umNUt68FRp+sr6eT9jhCKL5iElQTQaN/dCrQ3PhI
        MPeuOUeTZKrN5CNmao99MH/98S8fWrvSD/jhUQuglZVI1nm8sQZPHQtN0dfzXx4cpgMqwTiYAx1f
        3duaIAlHkEcfU27jCPFSyDP111/bUa26iet+D7UyX7Cc3wvOjgT5gKl0wZXomBYfLnIMydiemRm9
        go4XBs/QrtFkOpsjoNYu6xB9vMRi68NW5n0er2MIosxljhsxxHns9GoYKRadBXqquVmvJPj2A7Fw
        LAefJ9kLP15gtl3frae8/ETo+38pLB5NPvPFYg+5f6Bs0w06n8/W5gJpMa+JqyV1x6O6jNE+NhLm
        WIpqzdUxiUG51y5dnKU1HzFxWoRUOSH+tx7nR6s8IdgLRzzdUfv1NxVDmIQp8apbx6cyUjB88y7z
        5rfRiZ9Fbv7yA+W/fkXbq4hKXCyY/vPHgFr4l/eJq35uvP/lZ/1s63T88vrIukGGw1VbMKO2t/XY
        xMIFfvpsvN454tL1foHb+5qzwF/p6Ju3XNBhJriThSEYyzpoAaSlzjZOOdXdQhlFYO14YrfrsuCT
        N9UuzPZ6Q9xT9UBcxPJ3XhD25Ku33XJ7vwI0ZNaJw9gz+F0PWr2OZywOvmiN70BqYJs9pS+PVgn1
        ps8TGXIPxDiVQ8BELJewslofi3rY1Uy1I6oVwvQiRjk/u8/B2B5/PE5yPdcsZu6ooPD29cTPWAm/
        eV+pQHaHhGHpRHIOidfCl2+Z+ejWQZ+Uax2qQykR86rYSDIby//55c8/0Vz2tgNeeonxEtVdPS3G
        MQViiwZxzaLPmRyJIVwfbkTn6sotrtRrG1T94zHHHKWOohulQFuik29+7ebLaZjhfVI9+prVjo/c
        IyJqZedC1plX13PmgqQuT5sWS9FTyKd08iQl0z+IWHsk8vlJkky5XlZHZtn8GbBd34l/rz8oFqQb
        9485g3O9IiS8ydSiU9i0igrv3XZU10XQO1cnhfOu+e6PI97JfZaqCTg+Xubxy+LbdFsqy9O6xbfh
        fMv/8tllThrcHjsnnw/+Zg+pJjssGd+dNRweL+eXJ/DYOTEaBacS4eAVWyxc5cAassU8gp3jhrgD
        syzu39YZbE6pzr58FXA9tI5wq5UtnuEydl/+NZFnFhQL4trNZzNbSmiK6ZaR0aqD2TsGmfrZrnbE
        1cN1MojQUvVNHoTQRZxYUxODim4QOuQS7QfEhssYI08PzsTg066bfvn+L5/rI6/Zz99//R2+nNL6
        8TcE0iFiDqe3gH7qiYLeHRW2XdtSR4fHMoMFO4bM+OaH/rK+z9Cj/EW2n3785oWgQbtzUxG7a1U+
        OcazBdn21swvrk43L9pFr3bRUf3qfcPH4WlGYHRLEZfLO+Hcn44RKLcIMzMypfzTfwoVfedXVNvm
        YfDtl6fq77MFC2b10c0d1lVoUvlKzMg8JmN3Oh9BlW2Lff0i4YfL8Q3nekmwHJs94vtLmWq/fprm
        N7WGztZc5bc+eKlJMi9SKqFimw4kpLKeSxZ4Kazjl8UCp+wSxpxlD73iUmbiQ5P3umUXWmrIAgkX
        OrHmX72/aHggTrw/WbOIVRuMzxNR5JRBPgvOFdAyvl/wyl9tginLd6b2ux+2XRvBcptmKaSa6rAw
        Sw/B6C7Dt+Lp3vk778PB+6iNrVYvQ8z8RXxO2KVUKjXWJPebj0prVNF5RmyrEPLN66jzprcOiiIa
        xDJ04a++qriNMLmZ5SPoH04sgAVOzZz3fZ8zU8oK1WuHgLl6+EimT40idDj5MjE20NfzllilcjJS
        i+7Dg5Hw73wEYvrp/p0/NtXYAk/GGyHPUEyGo/iZ4fVpN1T0umvH9HFw0I8vouAMNSdBa6IrjXtC
        zHId8CmsHTiuMxWfSH7IeSkkKfryM7Hxy02+PNbCt/6pKkGLmBtdLmiOBIKF+gFo7G2hB1MCAWt3
        NcrF/rR5wrMi228e8TuuvtULKgg12fra0IQXgUvRV99YcnEXaHwVZwz3SXwRw+GfYPrOd5Cdhw1J
        u/Ut/85XY4jrrcocsfUDNBixrUm4avDi/naCeZumImih2DBvVN98FNdKC6rHHxTyu1xTc6dGQJ1y
        YmFv2MFy4dwqdBjcC1nX9ivpB+NdgRzob+KtbUAD5lREc+Z0xF/yV843588IX71i+JXaweiwU/x3
        3vU9nrV8F1sZbYzsQpWXlQWTfkhmUISM4La5Y2tykkWJeq0HjHAc1Oz1Sp3fPJKisJn5XL17X/XN
        xQfzakuDHpLIQVNI33RqpkXNz4diRJfisac/PhsX91UM86G6Yu1xQMH0LrYq0izq0tkMac1XgSOh
        K9JOLEBLLxnLPgJo6iKi6OtX/WdXC5DOlwMWVuWMGumJCng2VcVItvBynrkoRtMpytnV17/z4mel
        K96qSdh+YVT19OMHLux3xE2dfcLLzcmEV4d0vCy3QdBv215CCjgqs0J9Z4mH5DbKc4k4/fqBRY0h
        uvzlJX/JNzkVtHivbj6twowdflojvt8q2N/mM9tmwzofHX9ZoYMUK8QhhdHRGJUOxOPaJMYRnfh4
        uDwctSC9yRzmR4g/yFmEZVxfiOfgV94Wz/KiLfNOxmos36wp81xX/fIe0xsxQOO1FELI5XdIsnsC
        1rzaX3uIC7qhstet8mEjRClcHZN89TupV5tNpMKw3OQsYJsimbcwP0EOzDdbL1QjUIF+Mvi+/yFB
        OrzrUZ3DAu5StiT+w7Hyv/NaNRVMuvz679iVexUiplLmCaEZiOXmUYA39iNxzPCd02C1xqBVksdI
        1w85bygO4XOLX3gpHs5omr3UBFxnHfOp8bKaNbub6Jsv2enxjuvZWNz3f+cxG7RkHWPdB8Nznd2J
        rocTmmu9LdQ+8XziOksDSd4tpeozmlYEM7/JOVF2tnZ4N5iFnpNzziLL1NZRQsl3Pt193ydlcAqn
        9C8P0nirmFD5C4t5xrpGbCqQAJE+U+ZIacVH75jGKszRTHQZspoPcthDSl5vLAm+ZVHniSX0PT+z
        z0Swvtd3QRu/lInubZZdv2IrE/bwWuM6bBb56Dxz+m8/tMt3Pgup3CCBjku2O+vPZCZB94Rv3mX4
        JFr1VNmBgLBKZroA7cbZpZwq2Mm0ZmvxcOZffX1CJz9OjKRDlc/9duWrYqzfmb9jqO43vWf/9b9T
        qPuIgy7PEA2vM00uw8Gii0P0Bn9Tr4i3zefvPP2tImRdzyyUWr1bjd3tAhNdV8xu76t8ZCIPoW4f
        C2KHh3vCJDOIwYguNwxetwvmT9ZnMIz1lgTxxwjG2JYb+OrXl6+Hbt7LrgvhMPrEj/cj7+TMK+Hy
        lDHxU21bjy8T6fCd91DNmVn+5YM3wMc/MMNNqmS2PmMFoPU1M/fnjs9xaaia9OQmsY6foe6bizXK
        0DQblj40E/Wr4BqpFf3EZHuYsq4T70aqlvz508Mm6L1bIqqWwjFG1fZe00JWjpAsw9ffvDNLiocB
        vwQFv8n4qOddP2RwUbwNM/R7FUw//3LC6H8AAAD//5x8SZOCwLbm/v6KG3dLvwCZMnk7JpnNVEDE
        iI4OcEBRRIZMICPef3+BdbujF73qZVWFUiTnfNM5KhJ59Tvr/CIAmvLdI+UuUmsWcg/CZTn62PjN
        v7g+ekGZEImMonm3+iY/5jA0dj3i1T7vp6A9Ff+eP4KNZf3x34q35LDizbDiDeC52w5HAvwwysQl
        0j7RxCP+Ye3BfDjET03INy3Fx/uUzO+8i4G63Z2oBecKtKzsbtA7tibK1vkN4YStCd/NvqIR/34l
        445NL5herzYBXH8DzAk+jvrLN14CFJLpUDxy+B4Di27nu11O30PPwbPu69T2nqo1rXygrf6Non0z
        g/FzDFLQOjf2y4fZOAA6/fILNEfS9O+8OpfMmkZj4NY0j8p/56colmVGW+UU/fwX4RqGwEA1cYJC
        1lAC3C9ik99+Azi1vPbTu+V86XwILzufoyYLTmErUd5UU1mXqHN8fsNpkbwWrP4Mh7tRLGfRzD3t
        U90xOQXSJ2Q3+ryAnx7MRHiwpJXPlAPfONjhJQH0h/r2BJgeT7967dlLv3vqB9sp0ZrbrR8geT3h
        77wWfPuw6ZG1AfTt8YPd/UlPNj0KLsrPD9qFlrH5wPsNMEWOoyG7MfabDwLxeByo/XGifl7PD4J9
        9qB+d4hCKfTTBu64ZIvkULuVUx49OthF22Sdn1/ByOWqB5M7Non6vNzr9XwJfJvNgbqt+Krnfnn9
        Oz8M1rz07/X/+m0F/Nf/+P/YKNj8vzcKaiX4ICX3k5KcA0WH9bgAugUUh0tlDBdoaN+SWj1uwxnq
        QQXGg1xS/FKe1vQahQnG73dBluvJrdtIqWK4jdIbdifBq0W89V+g5swb9rqKA7MVdw2UNQSxa5/j
        sGH3VwN5lQVEss+xNadw8GCCm556J//L5tCCBXxLroFD9ewBJn8OjaZK4EI2d9UMJz5AhapsU0Ld
        dsJsib5bGSbppcaRdais+SR7AxyAb2HrvesZHYoogMk5K8jmFm+tCQtSoBZqvqXl8FWTZfsuBCik
        aYX3X7VPlo0QIKAE6E3N+Qksqo9TBMfz8iUyTJuSBU1rgsN5shA8yzSZSjIGQAYvHiMte4KhhdMC
        wBifKRJFVHaVZ++BmekTEV3LtDbax88hSkVKCE5ouPQgzkH4mmPq7h9Wv7mqmwa+7GdCXRqF5fxt
        Ow88Lq8PRel7C5aTWKZwA+onkgxNLLvOLWX4zu8ldg9HMyTcXS0AO+4Ewmp+ZMvG8GU4RImK+Gpr
        9YSL1QmW3LajhqOJ1jQJXQHWvxPKVwdrCrePCfD6rFDXk4JweX6/T6iE4nN9Xks4aoa3Zksgwdt7
        9QTTYlaxVpjmjL0bs8qJnF+c9snOGxyJLui/x7q35chXB1LbagPm1rMcdVYCFTuFLoExTqkNH/Oq
        6Ll3Z81BdmzhOFUGPeFkWy+LPHbg8bjdkTxAtV6u508Kg+1g4Ptl45eEV5ROXesHo/T9Zu22pTJs
        MxVQe/fye4l7vQRY1PIBX29y3rNY3qtQGYlJpqB8MbIsOwGy/EoIV5Z6TQ5LFcOX4+3xzjBwOS+6
        KsOkgCek3Ww1bFuqeABIpketsbfYlxWLB8EmD5Ek7V/W56lvYtApHMXhoe3BJMnXGyzCJ8ShpI3W
        bCuPG6zjco8kJdZ7hoDoQZbfCUbJyQXil8Ecmra/x/YB1dZEXYuAauLPGJnzrZybcddAJBJM3d6L
        67mpthD2e+JiXJzHnokDQMC3jiU2tW7oaWl1OpyfvUMG0zKTKmLeBZpKtUOfh3gJF3SwTP6BG44o
        eR3X07lwn/ANOBkB/uPU0lDVEArD7UCktR4EVxwJ0FH2wUHUPuv5ahxvkOe/Bg3lOQSLLUEHFvnz
        TeyN24LBwY0Anas1INDtbv1s8qENlQSm2NtZKaD4m8pANK0TemwKPxykOF3gTdwrVM+e93r44MKB
        4t3kcYSCq8W8wWhhsCUGvhdsANOmlkT1KFUnfIvckU1Xv2vhJXcgUnV1X84+zQa4u99norGbGU5D
        TiYQUd3Bp44k5TI8z+lff6jtFde95CwVqPZUpM7lwvqPt3wv4JzObzIy22Ti66THkPOFMzVa+8wW
        V5FvalxKPQFK2IfL6clUCFU6Elm5hckcSaoKc/NWoMmc+4TRYzWoTB4PdH2eydyoWwSD3QvTwHnu
        yg4e4Au2VdvhfFMs/fezGwhMXoOD9nchs4gEWgSPfOdRp0SbnsqVsoCDrh+pz9mbcuaQnsLMVC3S
        S/UOLLVVCWAbCwk+ptEULkgKCRSGy4Ei1WnrKei7G+wTziC8vD+FyxwiCE4y6RHrNh6bGTm/4HWb
        ehSfeZTQi9RW4DyAjEYcv7DJ58IBepP7oFZjdAk9PZwndMN1XHSdHbC0b65RxbwOqfU675Jp8T1b
        tUIvINMrJuUESF6ovr/BOBB4PWFPXYuBm5ofjNpMtSY9LhvYal1Mjbhvy+VR1gM8y4mJZlwS66+e
        wEFeHc9NqIfJ82VV3EwnjA8PsaQ5GvcqfzqM1HmTIJwfdm6ChxFBNLn815oipt/AUfFSmtJNHE7J
        Vb0BSzdNGqmgsOiYPiuVChBT38zMeha8vQx39+tMZL769NMQfgIo4ed2Pf9jyQpc36DfOQu2HnWc
        LOCUIXirdR57x8cA6NM9OhBxok7AXieMap1zg7xtMOpthJmNkbSo2u1xNslkniKLbrvtHj7sm0ge
        bOkZs/0GqWu/E/7wEBOy+Ziv3+uR9OyK/quplQBP1UvF95N2YOv55jB+fwocXdI+JLYNJuh0xpFo
        xmbHZrkjLWi2wweH5/idjD69EWBf70eMHEPuh5Q7V9BT3gN2nc2u7s6aksGHKSoIXDw9mbzutVc/
        8uFI/WoXWfP3aBINTFWK83PolGxbv29gv50rGj0zN5kuX6uCotWnRLnx6/OT+xh83dzAV9cK62nI
        mwUcx6mlBt6sDrVVMyB0Qkqmh7j0YwVnqBmS//nhKxs+x1kF087WaVCHMpg+z7mCHWiG9fm4NVPS
        KYPfhd+QytAGRpZo64GMu4vY+A4ZWLL2WAHUZzXGO4tLZjUKOYDlWcIWbJW6LQx0AY8mzWi0Sa7h
        oOcgB8Ux7ql1sq5s/qRRBZpXtsMhU4SyOZZHFdbxeU/EfmqS8QbYBKNjnKNnfGWMJdv8Be3BAdiq
        36hfnK0CoT1xDJ1E0Qbv3U0RQQFNE+Wn+mut+mr/w+u/+puO/bRXm04w8OUWo3r44XXSZBZhXHRl
        c+lINjgfHj01NNvshdK0W80YzzL1LoZRLuJGLzRXqmzqorsbfml85QByJQf73VgllN+G8Idf1Cxp
        kLzV99aEalryRD2IVTJHc+LA0xEjpL0CnLBqODUw2nkN4lL+bZF2ERE05+KCuDRs6vFO1VTdB9/T
        Wh9DyF7HWIUyJyp4rYdyLh3eAfoAd3i38vPy4fgW2EI00nJKEvCd3loOKMtiRI7nVzldsy6GreC5
        NFTC0Bputq9rY6dH+LTr23qKhqpV6UyORNQ/Tv2dncsTvEw1o1GnV2yGOzOADyo+6PbhVdaSmakA
        WsR8jKC3Dalgcx5o26lEc2srgFXKCUGnSj4UJ/czmErwWsD9IHtIe6RCMn0uZwJ8X8J/9TIKpZz+
        zg97cT8D5jseBKYYJ9jecR5jlXOT4fuJAzJzsZ5stI+RQz27nOn2emvqZWgVD4x95+OfPutyXxWA
        YyUc+aCtyyTutjOhG+cdRbQRLLb3BV192BcRSdY7SUZWLAEAku4h5XNtwmHyDBla2m4hyu1NwBy7
        nxuUfP2APTvU+gEXvPqHR9L2Qxnp0uQJA10MEF31lZhfi/jvZ35nceV8v8Qy6DihwftlefYzkmdH
        6+oLwC5SmoSVWuBB/K41/OOzRSx9D2JozDhoDMaGa/aMNQReDsby/mQNx16OIdjvZyRq/omR2mpF
        KMyZjzbCfWN1tebBPz2xm8WttYFOl8FLFREkBAT1zJ3rPaR3UcD6dTkDlqn4pXb+/Yu3wv0Yzprg
        6+CIfZO0IjqE7Ny+njCcPI58kwNgM7rLJlC5co82+b4FY1d7T9h7zRFNhk+Sfl+DPaQ9qrCdibrF
        iizcgyNnhuQjXt414XeHAG5PQ0ATXr8kcwf9Bvz4YSK5VbLy5F+gFU0uPe3Bc9XvSQrk+o6o+66O
        PY1fyg1QZ+tQ81L7oeBzIYHNwUlQc4yUhPVrojFwuUZd1nQ9sylRgTLYW7pdLKVkV7tCGjh6LU03
        wC3X/ulAuilTJJ/DJiEiaRsQwLNGA92ZAfViMweWOHKIwyMpJz+0CmhUPSOclpls01Q7CO+62WLL
        zcyS6Z88B/H2JFE8RVxJf3rVPe0f1LN5aA2mm2eQNtkXB0HTWAuvzC386SXFeNphF4m7JxS7osdO
        mhdgKfrcgUtoFhT/9FuyVwXw5Ud19TdvQN2tuYDy8xGp6b9hT+mzTWFioC/GHr6zYe03eEk3Jl71
        Rjk9+bsA+Idxod7yOvXT6kc1Wxtkwj+tT9jvM9CpygkhogpvI5yCxdyDbKYz4s8hBWQrL3tQKW7w
        Vy/sfr9zIGytCOsfeyqH+AL3IDkNVxrDCwLT6Gyf8KSmLXVDPLCh1m8OJMv7SyQa9eV8p0umnTz8
        Jlda+AlVUjmFq55BSqoo1gJeVwFuLy+DehnteuYc7AgmptDQLcd/yznqwB5ezoOCzevpU5MR5o0q
        dnlP5Lif2fL0gxvUySug93ye+r74dkT1w3uEuNVvzjD5QqBNdk/NyJCT1832TXhwKn89Px6wIXll
        UHKLlIYatthimHwBw2/v48DxkNUeWm0Ae98qaNBslnLevc8dXPsJTV0a1GQ0p4tW7DyF7r75LpyO
        t6kDq/7F+D1WPRvH+gXqVzyR+y8veKNShUlt+9iYs30yRUq7B63VG39+efWbCyy7zURNaNJ+CS9O
        DPzkxWFbCY16cc7nAt5269LmliAgcBkWgXBwGiKY3YaNsfu5/OqLTN71kYxXdHV+9//DA7YErUvA
        /e2ZhEMKZT+9AqsP7qh5wJk1RbruQefMlhV/JDCa71AAaVv2SKPFt2SahGxo78c92oAjBotkeg5c
        /R/a5J1nMVKaqRo99wY9DHC2JiUCHljxBVs0qdiHgEfx4ysckO3Qs2IyK3iZLgeicLe0blNpHoAK
        /AybkZEnDHmaAFzFM2g6XnG52PMlVn2bz7F/P+/KNV+YoHC/tOh7HKWe1IHXQRS6Ova/2zohn5zj
        YNlJEw5qXkrm3WFBgGykmqh3wNX9LpVieIwLjdRrPvK9X2JVe9TOhcwGlcuxJeEEo6QtcQjLdzld
        LpYKwktfkTF8WP18vIYxYE98JQ9gNtZoxd0L9tu0RH1qf3q26m04P78OEqakSyYHEwHGIw2JGjRN
        SNmVR/DDzjccHtDQs711QfCS7LZ49S8Js2FTAWmneng7lJdkMhvlz+8QwH+afl7SOINC8ZpxxJ8X
        a44JSeHx2EnUvSA+fJFkG0D23F2RzHIxZN8bbYD4Xjd+3O4esnieHXUbDA9qpKMNaNwf9vAUXu/U
        0F2QEEEPB+i6Jkd++D+De52D+hJwf/X+hc4z/fEPDaxhKamC8grKufukwVLbPfv2HwSAP0EkkWUI
        p1U/AxXOyZ+fpy7XqT9/hfWHuNTsUd0CQF/D8MfvMytUT42lm4oAbnDJ4tu5BaqkXMjC1Csj4rrR
        Kj5jlepd2vXL/EYRWPMcjPBIkqUC9gsSeiSY8uo+Yfw83ODqX/B97c/lE/IpYEcsYLPaPft24D4X
        0J1pRDZZ29dLl5ZPkHw4nXpxf2BTgZANm/vlSC/a6cgo30cdbIrmRs0n/+ong1djwNDI0y0XBoyN
        bqeC+3nJsNNPTsKudotgLVXdXz4olZoZwFtmZNRW+MoaSX/SoUvzmOKfX3G1bQVLzu3IIaMPi0LL
        fcE85xC2p/Hb09dGgcC3tZzi57UpJ/CRG/gejzE15M4qZ3vncbD5NC4adhlXE9ZEBWzM20hk3vGS
        zVXVXlDk909stLJcDmD9BAtpNyqB1kEPhbV/Rcwfeho9p6ZmjyR7QpWEIxKN8h3OPszSH79RvE/y
        ngZNq8OfX9wtndUzXzFizW+En95+lN1Pbz+neqLuJLT1JN/TXFNf3yfa3Ksna9P5IINzaYmkE3i9
        ZEk4FXD1O9QrznYo7BF3gRtSK0T94EP/l9fVb3XccdB7hwtr7EKjBadRfdL1etQEw4TgaooIHoWs
        npL9IoDf9XjUl+WsjY9IW/MQNBs0TyalKhoQOuGAd480TUhXsAwOMaNEEOmbdRnZpFCxhpk67tVI
        xtlJK/i4oRcO5mPDRnI4LdAoQo5I5iwwYl64FvCfek+mVR8uu6lW4QY8nmQRs3NP7AMoYPK4RFR/
        mEI4dub3Aq7WENJ4fpZWx+/OAVTTM4+3ST6Xy0HRnd/5ot7LNuV3xTNobtmODHItlcP1ajqwPiUu
        YeH06Kku7HUYnsuGCMZFDqcLrVNt5UeyhBpfMs7eLpALbxnVH2JcTwdzY8LczQ5IQHlrrXo1UI5K
        kFJ7X7z65ZscMzCHCsEeTa/lpCvFDXJedMFX5zuULFPdBp6SzwG12TEslS81oNZqbYzRyJ3CqUCO
        DTrwGug2jSZrya/xHsxvoBO1KRNr/uWHigP7P//IfnlA+TpC+sPzeTtWJlz7FXFzu6/n082/gVU/
        YS+5TyUb066Ct/R8pqYqHPrJ1KcnyA+2TsMDiurJZJMIDufFIko3VuV8tCJTNWowYte7Bmw52+cI
        tPrZxZ5y60viOlMAZxy21O9GPZH21/ICbrXJ4+1inUuWbPcNTAg6oOd1dpj0vsgy5D+PPXnIe9ua
        tx8daT9+YBeaAEYv+hN0FTawHaQ06ZN4HwG7u3GENeW3JjaXyBAkz56A095jQ+tPNzjJvYB8bS+C
        peU18ZfPEUmWjXL1my+AHkJOMwdbtRApbays9fjTt2wZjocBtNA84lCeezZ63l0E462rCKdWH/BN
        wMgpiztVv/6tNxzysj+8ECz7aM3CM/eghQqbbEqzA238BbIazgugppidayZI3A3G26NEA2l+lpMU
        Xybw8osA75pnHH42y0UA8d19I/51f4JhCD/ej0+psU+2/cwMxQbN/XbEtq/urYYbVB02bvbC7lsV
        2GwcXgX81Y+t8HrYD8mQQkvhZezKspFsvk8z1fKruCVLquCa+Y4OQVeefLpruR2TDpr6hO3yDKjt
        ZT2jP/2x5m3UW/lws/odmJuXAkevpbaWp9l18O1Lm796qlJ7LkASEIH619PBYhfOS9Vb0Hd/fPfT
        Z7DT0BcxK/7W/e/9V76lhg4UMCI+l2FgXlwcOqkMlge1AnjM5ZmGa/677Py3CfVD86S7Xg8sKunF
        AP1+oNggqlD3b1PpfvMgHKx6XayOlgpR7nwJIAepnNXty4PKKUI4U0cYTnyNLzC/38yfHy8J78yZ
        dnzoNblGR7OU9E9eQEna8H94+DePWv3nOv/IwnG6HxsIiJrRX944R/bygr88bO33hG1kof3lz2s+
        KoUkvKA92D9ySp3m6dViDpkH9ltWkRfK23C47oMb3MFrgf1Vn83ylZrQUjQZu1JXl3RUe6TYFv1Q
        FD12Fjs90BPCIDap9dDm/mlzpSqpxB/JGCRbMOmL2v78DuEmIbam5/nuqSHuJSKL7ZQMr08Kf36T
        2nv9nUxc5gpwG5AHDvcFCVd8eMI1H8auoYkJ3dbjBRhS+MHGEiA2ax+/gD99J7BtVG4ENA0/vKSh
        fBr6qWa3HBxTXV3P/1TOv7xIdyIRGyL69OxEUAB/+dmPT8dPaldQg8JEHf6h9otan9pf/kzOx/FU
        z49alqFTPx4I7F5JON0PegvX+RACKJ/7zyPJ/viFmnh3rpleTQ08ebs33eZ7jwnNg2Xa8dlWa3+H
        jP7yNMG86hTz1RzS1e+Bxk1fGAXbOZnnBrwA6Mw7DVXWhsMecTe45pErX6Vs/f89dXGXiswLNyZs
        SIYM/PLLKObKmunC3tQCjCIa9puyn1o6B9ra70g+giGcozAuwO9+5qNrW8JPL615wboRisBfPqHN
        Q0st3fGSxhQ1Gaabc0ri2Er7+cS1T3gwuhgJmVhZa36fQaeuH2jSer6m3OmNoH3fxvivXuRqnrTA
        dmxyWM+b9spoQgrFK9bfzsDmBLwhvE4KRFKb7fthfJMC5rvXExvHh/VvPh11E6z53yccrSYwQZSp
        x/X6erh5JygVUUQ0xHhBZeMx6y9Qf4hbwvPOzEhE3wSi3P5S6yTBZArjR6EllyalFtnWYdMuYgS7
        amcgkvLv3zwyh0b1ZRgBsu2XevIz9TdvXue9YOGTiYPuuByxxzttQncPTgbHUPEwPmkzWPPbVDOv
        KkeWJ/9a+Voz4S34dtix2Acs5lfcQ2B8Q2oblzwcs6kjf/1kBwTVY2jB/MffSGzgB7DNkgrauU/O
        RHgcr7/6z1X6bWrsvh0xWf2O/PPH2Aono9/85i/93fGxcfKrpCqQ4wDTN1yyoR+tHh/aFMCj9Dyh
        OT58reX+2GbQfej7Fb8e1my+LUFd+QX/+GZe9EX94TlZ9Zu1ROK2AktWOBS9HZ5934qrw2Gor4Rr
        syJc/aYNL+B1xgG5h+DXb0AsQo9w8Pkqv9NwjOA676UIkHc9XS6hCiXfPPzN64k7TCL45ZcB2Ub9
        rEYW9783Cv7xz3/+z993FjTt9fZeFwPG2zz+x/9ZFfiP4lr8hyCIf19sQIaiuv3rP/+9gfCvb982
        3/F/je3r9hn+9Z//FP9WDf41tmPx/r9+/Y/1Qv/1j/8GAAD//wMAjOe+STBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c07af71754-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=1RVzeqm003kDD5kAk._MtZS1D7NNCIKXez_eMV0wWXs-**********-*******-l6FtdYlkVNVDu7QU1IZ.FIzXNTCiHWY89UfOZMXxxXdEbFIOgMHZUoHY3hTrPoxp2xCeXl0jxOHyeSRlglZ4Bw;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=8Ou2w20OaaDmDHijuPj32K17XBNlds4J9z79rZGaGU8-**********393-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '19'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999998'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_88bfda0af149b8c1e82f346ebf4bd93a
    status:
      code: 200
      message: OK
- request:
    body: '{"input": [[9642], [2822]], "model": "text-embedding-ada-002", "encoding_format":
      "base64"}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA1SZyRKqzJaF5/UUJ/4pFSEikJs7oxPpzESwwRqBIgJ2NJlA1stX6LlRFTUxQkkF
        Ye+1vrXzv//jz59/3nldXIZ//vXnn0fVD//85/ezazZk//zrz3/9x58/f/789+/1/60snnlxvVav
        8rf8d7B6XYvpn3/9Ef/3k/9b9K8//yg9KPSS39NuHOTEhaVc62zbhKtuGuRzA5Gn+Mx/tFM3X0pu
        w3m7bJl9NR/JaEilrDHvYODJu6zRaHFdgD1jGp7NENfiObzJCOZ4JngR82BSb7OpRTG2iXHY3oKp
        XGYCJOnhxpy1qXH6OuijdiLOwJzV/ZlwXVFT9SOkb2JKqReMR7E+wmPj36jyUI/1VB0vonq4LhZU
        8M5P1O+TT4Vs9fjAsznVfGz0RwMLHYd4DIQdmo+O4oMdWz2WUo3zab33YhQsrj1bl9vO4hJT9yA/
        njkh86G12C6LBXVky5Lp/bO3+rRQS+SKs4VXd9tOxsEoK5gV3BP9prz4OEakR4IBPrmSgQfjyRFj
        EJYPjSrHo2RRoTMu4L+1LVUPlcnF4lk34NnsRWG9MTl7jckbxuH5opq82XbTuXhhQOdCYwZ+fPJ3
        t3YvKHZyjLVsWCfcU+snKqhkUWWDztacHlobrjTqSZg6NBhxMBaytzGBoll91KPZ4Ag9zueUWLuy
        tajbiSEYFUuZob3LZJzCqULq8T3gqcvr7/2Y98BvOKJ8fdrn40Y4C7B9uB8WkMU16SSmHoGpDBGz
        uibBZDYhheGVjWS7to81v59fqrY1LhmdNqdTPi7CNESVBCE+HI+XfAq8e4EeG/dGru3uwcfXIZDh
        cV086TgE92Qq9jsX5G1yZ+ZihQJqDg6F7/PAq11p8ZUjH10A39wT55VG9birGwHM7c4nnrLsu8lX
        aQ+fZRwye2GYnbT25xR1ouWRrYnvwZh5BgU3MDjtjnbH6eExlFB+zBnv8pVZD8Jd61Up4gVW5ve9
        4+XSsaErHJ0qSanXfHkCG2kdPIhVzVY+euqso8IVb2TNpy5/rw1F+Hs+HHQDGh1fq0AInCfB1l5O
        5mZ1sJGpRAkWXHHqBlmQXXjdLJmEQN1glXwCGU4hT7FcsYjzcnMz0XpwlwRf5S6Y3yb1YadFE3O7
        VqznzdIrodhmAzEvQR+M5pA06HGnMdl22SefYmElwgObJjNLliVjqkY9RFXwJmGoWMH0aLm/0GVs
        MFLeqprrr0+FZm1WSYhNp17u6jpFwt0OiEONIZ9WQU7RUo9q5mVDV1O28XRFb65HpvuREnTP9Jqh
        dO+tGJkPfjAtRjlT4yQ64r2/dIPpIboXeBkHzNzl3crHQc5dyPLYw7NhBdYoaIcC4OMe8OMw42Bq
        VscW5oS4BGtNlvSGVKogrVOXpBWLEG17LUZ8lkuSUtmpx2+/wqYDjOF5b9GwW0KEzsvowchwXuSM
        nZ0GwtP8JMbzlibjLdNt7cx2Ol6GqzZnjX5yQTHPG+bNVpjwTGr36FtfNE9mu14J97lRhbsTMF3U
        7vW8be8UueR9Zf51bwVLQctVeFy1J/HOzdviujJnmi3InG1PtyQZOxzqSDojFf/6vZX7RPirr1vJ
        vAVvMmYh5GteEudmVsFYCh8Jkl2ZEnwS625+m08fSK08WaAdqm4A3wih2ysuI3gn5PO33tXtWC/o
        AdVBx+GRVbAy9kuyX/d2PR+MYIad6B6wMlt9Mtf0XKEd0Rlz6ueYtCJWHZQJQkrz7SAl/aY3bDhf
        zQ1WLNAt6VNPPZy3q5YFQifwMSm3OihbUWYOfZo1u23eM7TQtlR7HPKAI9sR0G99GCq1NVa3rocM
        OouW/TMMOFzWGFpeYozYRshZQx0MvTGIzA00IeHsjBt14e3f7IzqRz5K5q6Hs3lW8KwZD8Tsea5Q
        Hh2SQVbKS0IzVxSh5RUm/oVt+cPbeqm63x/fv9/L6Wf3ltDY5AyLpyoMWJMvj5Cpmc8CpTZzSbiL
        PvKnaMPwPTmgyZSuT2Sc9ibm3fCyhvgTjaDc7y4evc2h+9tvDVUNtr0fzeBbTw6QezsQsj+GfDyr
        jgDb1bli5jbUERW07IjuRhLhVmwt1GdDXfzq8Xecr+62LmkT3VQUkqC1Jml/N5FVTy1xrvtPMqof
        XYW37RXMiNZm12ee18MgbFfE14tPMu+Wc69+alEip836ydnjXvXwcRaUmIz1SXf5aLbaFbZO1v6j
        tuZ5kDFantMFvj9vck7RtHiiXVQ4hCjV0uqLZ9fA936QQ0CdejqdrSeUN/VANqxXEFtoRFe9tIh/
        fpX0z7ST4P6pB7Iu5zLhJKhMkKKpYG5+L1C/5bMLT1xpv+fH55PWjeq4eVmEDIJYD++xmFE2bWMq
        dq2K5sajOsrXU4mHwzbM53TSI02AW0lc2detlRtdChQ1hUZ/9cOJue/RRVrNeM1DyZq78jyi7/Xg
        FZwFi6rH2gVNIAWxpXYIhsG4hoix44N4m1PLR0G7Fuh9158kmCbT4uh4E+HeaDc8B/2mHozc8NH7
        8RSx6kZex33rhWHl4pSYS/KoaVx6KshOkWGBe9eE8sVqD86mf5ONR616ZiLdg/ZKT8QU2zGf5zg2
        odTnA/HKrY0Gsd2JwJ7YI+H1uQn4nk7xT//omHZSMGyqGSPdci6E4F2RDyqKMCSzEjP9pmw4k9h8
        RE6XrRg2xzaYJzXvYb5kOiHTuEfzYSVlv35m4aulSc/z6oLa2JGJnYwd7xt9aEDpBYWFK4zRjw9A
        D0OZbNxHaY2FDAXY99Aj2Az2fPjUPEKH9xOT7/0O2OPe9miTbmvivnmaT+PNlWAn+gfmLh5tN3PP
        pkDMqic/vuRcmjHqMKqx5mwzq7t8lrZa+ZrFiiU3LH5IEhua/Y4z3zBPHZUUI1R/eqxymqJ5Oiig
        3QbrQ7zT7VyP6seVYWsUGZXJuO7mIjBjeJ4+GVWaUOP9tRQwXCK5xQLkEqfNw8Yg14lDbD3sOrZd
        wxvG5swwfXKKfv6resHLZ+v1ug3465XaUAv7lgRq/+DzUXyHINhbi5gCXdXjxy1TkEkjEVcEGvBr
        yWYkecKRhOxC62FtjClU2dpmDlzaZJauta5eLvsci1QO+Vjs4x510V4lW7k3OdeLfQiFIQDTV+Rd
        j5kkz7/3dMGLoWZ86GW1vqA9FV+dwUfTnSTYFe6I0WdxR315Fo7q67WfmetWHHG5zzLQylJk+En0
        ehr8qtJmJewx36M951uYTLgX7omEK24Hy87WfNjfxvOXD8xu5fgvG72dCyehEz3zcUiUDO5y75NA
        Pp84l8wgguU5W5DtURWCnnsbEZQ064h5l6987mz+1KhTTVRd4jmgqWpJIEwGI3p4eNV89RxMtCv8
        EavWYV2PyL6mYMdGTzbT9E4mfVSEXz2z9dev+/XeF6GzrjPW2t0DTaVwLNHLOGHi3SQW0NFZxGgd
        7Sh9Thf52++HEFlg13g1vm3OPUsWIOmVAdcwlZxmQ3lEOkMb/Ig+fk1//boqC+lvf/DyrLxRaFsI
        84t748OPh37+4rzSseZl1B9Re60+dIGWdcBXARZRfsISMwjtEa/1UgYk7T7MdJdBMsOqF9Rel3df
        HtdzkXWDipjkDUz/pKr1/b4EePlp2I9vucjjCC2EyxZPDk7QTPaj+pcvCk9bWiyL9X/rt9W5Oz5d
        T+z44w2qwqUJOJdUrKz1vU/FT8rRtKnUEDVvvSDmKXKtFR8aWRt5E375wwzGzVkOIdq8d+xoKZn1
        zQMAj/3Sx7JxeKMeHm8BVPUIGCm1mayafLmHfk4COpjYsMbLSW2BvKuI6Ieoy+m5GFoII2RRZTUK
        wXTh6wo2ZO2wTbE4IL7aX1z07R+GBWqjUa7zEr71QuzgfEz4gtwECAzSEZ1d3/m8Mm0RXAOd8HOR
        POshFhYSiPtdhgVPOwRTQxMHVm6YEqtGTz5YpztGdftaYPTJ+no6aY8jhOIrJkE1ETT6R6cCzY2P
        BHPvmnM0SabaTD5ipvbYB/PXH//yobUr/YAfHrUAWlmJZJ3HG2vw1LHQFH09/+XBYTqgEoyDOdDx
        1b2tCZJwBHn0MeU2jhAvhTxTf/21HdWqm7ju91Ar8wXL+b3g7EiQD5hKF1yJjmnx4SLHkIztmZnR
        K+h4YfAM7RpNprM5Amrtsg7Rx0sstj5sZd7n8TqGIMpc5rgRQ5zHTq+GkWLRWaCnmpv1SoJvPxAL
        x3LweZK98OMFZtv13XrKy0+Evv+XwuLR5DNfLPaQ+wfKNt2g8/lsbS6QFvOauFpSdzyqyxjtYyNh
        jqWo1lwdkxiUe+3SxVla8xETp0VIlRPif+txfrTKE4K9cMTTHbVff1MxhEmYEq+6dXwqIwXDN+8y
        b34bnfhZ5OYvP1D+61e0vYqoxMWC6T9/DKiFf3mfuOrnxvtfftbPtk7HL6+PrBtkOFy1BTNqe1uP
        TSxc4KfPxuudIy5d7xe4va85C/yVjr55ywUdZoI7WRiCsayDFkBa6mzjlFPdLZRRBNaOJ3a7Lgs+
        eVPtwmyvN8Q9VQ/ERSx/5wVhT7562y239ytAQ2adOIw9g9/1oNXreMbi4IvW+A6kBrbZU/ryaJVQ
        b/o8kSH3QIxTOQRMxHIJK6v1saiHXc1UO6JaIUwvYpTzs/scjO3xx+Mk13PNYuaOCgpvX0/8jJXw
        m/eVCmR3SBiWTiTnkHgtfPmWmY9uHfRJudahOpQSMa+KjSSzsfyfX/78E81lbzvgpZcYL1Hd1dNi
        HFMgtmgQ1yz6nMmRGML14UZ0rq7c4kq9tkHVPx5zzFHqKLpRCrQlOvnm126+nIYZ3ifVo69Z7fjI
        PSKiVnYuZJ15dT1nLkjq8rRpsRQ9hXxKJ09SMv2DiLVHIp+fJMmU62V1ZJbNnwHb9Z349/qDYkG6
        cf+YMzjXK0LCm0wtOoVNq6jw3m1HdV0EvXN1Ujjvmu/3ccQ7uc9SNQHHx8s8fll8m25LZXlat/g2
        nG/5Xz67zEmD22Pn5PPB3+wh1WSHJeO7s4bD4+X88gQeOydGo+BUIhy8YouFqxxYQ7aYR7Bz3BB3
        YJbF/ds6g80p1dmXrwKuh9YRbrWyxTNcxu7LvybyzIJiQVy7+WxmSwlNMd0yMlp1MHvHIFM/29WO
        uHq4TgYRWqq+yYMQuogTa2piUNENQodcov2A2HAZY+TpwZkYfNp10y/f/+VzfeQ1+/n7r7/Dl1Na
        P/6GQDpEzOH0FtBPPVHQu6PCtmtb6ujwWGawYMeQGd/80F/W9xl6lL/I9tOP37wQNGh3bipid63K
        J8d4tiDb3pr5xdXp5kW76NUuOqpfvW/4ODzNCIxuKeJyeSec+9MxAuUWYWZGppR/+k+hou/8imrb
        PAy+/fJU/X22YMGsPrq5w7oKTSpfiRmZx2TsTucjqLJtsa9fJPxwOb7hXC8JlmOzR3x/KVPt10/T
        /KbW0Nmaq/zWBy81SeZFSiVUbNOBhFTWc8kCL4V1/LJY4JRdwpiz7KFXXMpMfGjyXrfsQksNWSDh
        QifW/Kv3Fw0PxIn3J2sWsWqD8XkiipwyyGfBuQJaxvcLXvmrTTBl+c7UfvfDtmsjWG7TLIVUUx0W
        ZukhGN1l+FY83Tt/5304eB+1sdXqZYiZv4jPCbuUSqXGmuR+81FpjSo6z4htFUK+eR113vTWQVFE
        g1iGLvzVVxW3ESY3s3wE/cOJBbDAqZnzvu9zZkpZoXrtEDBXDx/J9KlRhA4nXybGBvp63hKrVE5G
        atF9eDAS/p2PQEw/3b/zx6YaW+DJeCPkGYrJcBQ/M7w+7YaKXnftmD4ODvrxRRScoeYkaE10pXFP
        iFmuAz6FtQPHdabiE8kPOS+FJEVffiY2frnJl8da+NY/VSVoEXOjywXNkUCwUD8Ajb0t9GBKIGDt
        rka52J82T3hWZPvNI37H1bd6QQWhJltfG5rwInAp+uobSy7uAo2v4ozhPokvYjj8E0zf+Q6y87Ah
        abe+5d/5agxxvVWZI7Z+gAYjtjUJVw1e3N9OMG/TVAQtFBvmjeqbj+JaaUH1+INCfpdrau7UCKhT
        TizsDTtYLpxbhQ6DeyHr2n4l/WC8K5AD/U28tQ1owJyKaM6cjvhL/sr55vwZ4atXDL9SOxgddor/
        zru+v2ct38VWRhsju1DlZWXBpB+SGRQhI7ht7tianGRRol7rASMcBzV7vVLnN4+kKGxmPlfv3ld9
        c/HBvNrSoIckctAU0jedmmlR8/OhGNGleOzpj8/GxX0Vw3yorlh7HFAwvYutijSLunQ2Q1rzVeBI
        6Iq0EwvQ0kvGso8AmrqIKPr6Vf/Z1QKk8+WAhVU5o0Z6ogKeTVUxki28nGcuitF0inJ29fXvvPhZ
        6Yq3ahK2XxhVPf34gQv7HXFTZ5/wcnMy4dUhHS/LbRD027aXkAKOyqxQ31niIbmN8lwiTr9+YFFj
        iC5/eclf8k1OBS3eq5tPqzBjh5/WiO+3Cva3+cy22bDOR8dfVuggxQpxSGF0NEalA/G4NolxRCc+
        Hi4PRy1IbzKH+RHiD3IWYRnXF+I5+JW3xbO8aMu8k7EayzdryjzXVb+8x/RGDNB4LYUQcvkdkuye
        gDWv9tce4oJuqOx1q3zYCFEKV8ckX/1O6tVmE6kwLDc5C9imSOYtzE+QA/PN1gvVCFSgnwy++z8k
        SId3PapzWMBdypbEfzhW/ndeq6aCSZdf/x27cq9CxFTKPCE0A7HcPArwxn4kjhm+cxqs1hi0SvIY
        6foh5w3FIXxu8QsvxcMZTbOXmoDrrGM+NV5Ws2Z3E33zJTs93nE9G4v7/u88ZoOWrGOs+2B4rrM7
        0fVwQnOtt4XaJ55PXGdpIMm7pVR9RtOKYOY3OSfKztYO7waz0HNyzllkmdo6Sij5zqe7735SBqdw
        Sv/yII23igmVv7CYZ6xrxKYCCRDpM2WOlFZ89I5prMIczUSXIav5IIc9pOT1xpLgWxZ1nlhC3/Mz
        +0wE63t9F7TxS5no3mbZ9Su2MmEPrzWuw2aRj84zp//2Q7t857OQyg0S6Lhku7P+TGYSdE/45l2G
        T6JVT5UdCAirZKYL0G6cXcqpgp1Ma7YWD2f+1dcndPLjxEg6VPncb1e+Ksb6nfk7hup+03v2X/87
        hbqPOOjyDNHwOtPkMhwsujhEb/A39Yp423z+ztPfKkLW9cxCqdW71djdLjDRdcXs9r7KRybyEOr2
        sSB2eLgnTDKDGIzocsPgdbtg/mR9BsNYb0kQf4xgjG25ga9+ffl66Oa97LoQDqNP/Hg/8k7OvBIu
        TxkTP9W29fgykQ7feQ/VnJnlXz54A3z8AzPcpEpm6zNWAFpfM3N/7vgcl4aqSU9uEuv4Geq+uVij
        DE2zYelDM1G/Cq6RWtFPTLaHKes68W6kasmfPz1sgt67JaJqKRxjVG3vNS1k5QjJMnz9zTuzpHgY
        8EtQ8JuMj3re9UMGF8XbMEO/V8H08y8njCQqf/POd//CR5ryibByk5g1iakLMM8Hjxi//S+hCxuQ
        KV3RQTJvVvdMDykExrbDC7VLu9F/n7J/7z+ipWX99b+v3tLdV2/6r96ghVBsSSjCizMuzaH2CscF
        XtytCE27XVxpYrp8M3K4jcn0SNsYqevtiVkwlejN87YA9/A28fG7f0MFcW3C4xmVLFw8mv8BAAD/
        /5x8S++CwLLn/nyKk7NlbkBe3dwdAvK2WwERk8kEfCAoIo9uoJP73W/wf2Yyi1nNUg0iRVX9HlUS
        j3s2vWByu1kEcP0dMNv/2OrP33gJUIinY/7M4Hv0TbqbH1YxfY89By+6p1PLrVRzWvFAW/UbRYdm
        BuPn5Cegte/s5w+zcQB0+vkXaA6l6d9+dSYZNQ1H36lpFhb/9k9RJMuMtso5/OkvwjUMgYFq4gSF
        tKEEOF/EJq/9+nBqee3Hd4v52nkQXvceRw3mn4NWoryhJrIuUftUfYNpkdwWrPoMB/tRLGbRyFzt
        Uz4wOfvSJ2B3Wl3Bjw+mIjya0opnypFvbGzzkgD6Y32vAKan8y9fe/bSH676wVZCtOZ+7wdIXhX8
        xWvB9w+bnmnrQ88aP9g5nPV40yP/qvz0oJVrKZuPvNcAQ+Q4GrA7Y7/5IBBPp4FaHzvs5zV+EBzS
        J/W6YxhIgZc0cM/FOyQH2r2YsvDZwS7cxev8/AZGLlNdGD+wQdTq+qjX+BL4NpojdVrxVc/98vq3
        f+ivfunf8f/6bQX81//4/9go2Py/Nwpqxf8gJfPiglx8RYf1uAC6AxQHS7kdrnCrfQtq9rgNZqj7
        JRiPckHxS6nM6TUKE4ze75wst7NTt6FSRnAXJnfsTIJbi3jnvUDNGXfsdiUHZjPqGihrCGLHukRB
        wx6vBvIq84lkXSJzTuDgwhg3PXXP3pfNgQlz+JacLQ7UiwuY/Dk2miqBK9k8VCOYeB/lqrJLCHXa
        CbMl/O5kGCfXGofmsTTns+wOcACeic33vmd0yEMfxpc0J5t7tDMnLEi+mqvZjhbDV42X3TsXoJAk
        JT581T5eNoKPgOKjNzXmCphUH6cQjpflS2SYNAXzm9YAx8tkIniRaTwVZPSBDF48RlpagaGF0wLA
        GF0oEkVUdKVrHYCR6hMRHdMwN9rHyyBKREoIjmmw9CDKQPCaI+ocnma/uambBr6sKqYODYNi/rad
        C57X14ei5L0Dy1ksErgBdYWkrSYWXecUMnxnjwI7x5MREO6h5oCd9gJhNT+yZbP1ZDiEsYr4cmf2
        hIvUCRbcrqNbWxPNaRK6HKyfE8qXR3MKds8J8PqsUMeV/GCpvt8KKoFYrfdrCUZt667eEojx7lFW
        YFqMMtJyw5ixe2dmMZHLi9M+6WWDQ9EB/fdU95YceupAakttwNy6pq3Oiq9iO9clMEYJteBzXhk9
        9+7M2U9PLRynckvPON7VyyKPHXg+7w8kD1Ctl9vlk0B/N2zx47rxCsIrSqeu+YNR8n6zdtdSGbap
        Cqi1f3m9xL1eAsxr+YhvdznrWSQfVKiMxCCTX7wYWZa9AFl2I4QrCr0mx6WM4Mt2D3i/3eJiXnRV
        hnEOz0i7W2rQtlRxAZAMl5pjb7IvyxcXgk0WIEk6vMxPpW8i0CkcxcGx7cEkybc7zIMK4kDSRnO2
        lOcd1lFxQJIS6T1DQHQhyx4Eo/jsAPHLYAYNyztg64hqc6KOSUA58ReMjPlezM24byASCaZO70b1
        3JQ7CPsDcTDOL2PPxAEg4JmnAhtaN/S0MDsdzlVvk8EwjbgMmXuFhlLu0ecpXoMFHU2Df+KGI0pW
        R/V0yZ0KvgEnI8B/7FoayhpCYbgfibTmg+CIIwE6Sj/YD9uqnm/b0x3y/HdLA3kOwGJJ0IZ5Vr2J
        tXFaMNi4EaB9MwcEuv29nw0+sKASwwS7ezMBFH8TGYiGeUbPTe4FgxQlC7yLB4XqafWohw/ObSg+
        DB6HyL+ZzB22LfR3ZIsfORvAtKklUT1J5RnfQ2dk083rWnjNbIhUXT0Us0fTAe4fj5lo7G4E05CR
        CYRUt/G5I3GxDNUl+asPtb3hupfspQTlgYrUvl5Z/3GX7xVckvlNRmYZTHyd9QhynnCh29a6sMVR
        5LsaFVJPgBL0wXKumAqhSkciK/cgnkNJVWFm3HM0GXMfM3oqB5XJ45Gu9zOeG3WHoL9/Yerb1b7o
        4BG+YFu2Hc42+dJ/P/uBwPg12OjwEFKTSKBF8MR3LrULtOmpXCoLOOr6iXqctSlmDukJTA3VJL1U
        78FSm6UAdpEQ41MSTsGCpIBAYbgeKVLttp78vrvDPua2hJcP52CZAwTBWSY9Yt3GZTMjlxe87RKX
        4guPYnqV2hJcBpDSkOMXNnlcMEB3cp7UbLZdTM9Pu4JOsI6LbrMNlvbNNaqY1QE1X5d9PC2ea6lm
        4PpkekWkmADJctXzNhj7Aq/HrNK1CDiJ8cGoTVVz0qOiga3WRXQb9W2xPIt6gBc5NtCMC2L+5RM4
        yqviuQv1MLmerIqb6Yzx8SkWNEPjQeXPx5Hab+IH89PKDPDchhBNDv81p5Dpd3BS3IQmdBMFU3xT
        78DUDYOGKshNOiZVqVIBYuoZqVHPgnuQ4f5xm4nMl59+GoKPDyVc7db4nwqW4/oOvc5esPmso3gB
        5xTBe63z2D09B0Ar52RDxIk6AQedMKp19h3y1pZRdyPMbAylRdXuz4tBJuMcmnTX7Q7wad1F8mRL
        z5jlNUhd653wx6cYk83HeP2OR1LV5f1XU0sBnsuXih9n7cjW+GYwen9yHF6TPiCWBSZod9sT0bab
        PZvljrSg2Q0fHFyidzx69E6AdXucMLK3cj8k3KWErvIesGNv9nV30ZQUPg1RQeDq6vHkdq+D+pGP
        J+qV+9CcvyeDaGAqE5xdArtgu/p9B4fdXNKwSp14un7NEopmnxDlzq/3T+4j8HWyLb45ZlBPQ9Ys
        4DROLd3izapQWzUFQickZHqKSz+WcIbaVvI+v/7Khs9pVsG0t3Tq14EMpk81l7ADzbDeH6dmSjKl
        8LvwG1JutYGRJdy5IOUeIt5+hxQsaXsqAerTGuO9ycWzGgYcwPIsYRO2St3mW3QFzyZJabiJb8Gg
        ZyAD+SnqqXk2b2z+JGEJmle6xwFThKI5FScV1tHlQMR+auLxDtgEw1OUoSq6McbiXfaC1mADbNZv
        1C/2ToHQmjiGzqJogff+roggh4aBsnP9NVd+dfj167/8m079dFCbTtji6z1C9fDr13GTmoRx4Y3N
        hS1Z4HJ89nSrWUYvFIbVatvxIlP3ut0Wi7jRc82RSos66OEEXxrdOIAcycZeN5Yx5XcB/PUvahTU
        j9/qe2dANSl4oh7FMp7DObbh+YQR0l4+jlk5nBsY7t0GcQn/Nkm7iAgac35FXBI09figaqIe/O95
        zY8hYK9TpEKZExW85kMxFzZvA32Ae7xf8Xn5cHwLLCEcaTHFMfhOby0DlKURIqfLq5huaRfBVnAd
        GihBYA53y9O1sdNDfN73bT2FQ9mqdCYnIuofu/7O9rUCL0NNadjpJZvh3vDhk4pPunu6pbmkRiKA
        FjEPI+juAipYnAvadirQ3FoKYKVyRtAu4w/F8eMCpgK8FvA4yi7SnokQT5/rhQDPk/BfvoxCISe/
        +GE36mfAPNuFwBCjGFt7zmWstO8yfFfYJzMX6fFG+2wzqKfXC93d7k29DK3igrHvPPzjZ13mqQKw
        zZgjH7RzmMTd9wZ0oqyjiDaCyQ6eoKtP6yoiyXzH8cjyxQdA0l2kfG5NMEzuVoamtl+Icn8TMEfO
        5w4lTz9i1wq0fsA5r/71I2n3oYx0SVxBXxd9RFd+JWa3PPp7ze9Nrpgf10gGHSc0+LAsVT8jeba1
        rr4C7CCliVmh+S7E71rDPzxbxMJzIYbbGfvNlrHhllaRhsDLxlg+nM3h1MsRBIfDjETNOzNSm60I
        hTn10EZ4bMyu1lz4xyf2s7gzN9DuUngtQ4IEn6CeOXN9gPQhCli/LRfAUhW/1M57fPFOeJyCWRM8
        HZywZ5BWRMeAXdpXBYPJ5cg3PgI2o4dsAJUrDmiTHVowdrVbwd5tTmjaeiTuDzU4QNqjElupqJss
        T4MDOHFGQD7i9V0Tfn/04e48+DTm9Ws8d9BrwA8fJpKZBSvO3hWa4eTQ8wFUK3+PEyDXD0Sdd3nq
        afRS7oDaO5sa19oLBI8LCGyOdoyaU6jErF8djYHLNOqwpuuZRYkKlMHa0d1iKgW7WSXSwMltabIB
        TrHWTweSTZEg+RI0MRFJ2wAfXjTq6/YMqBsZGTDFkUMcHkkxeYGZw23ZM8JpqcE2TbmH8KEbLTad
        1CiY/skyEO3OEsVTyBX0x1ed8+FJXYuH5mA4WQppk36x7zeNufDK3MIfX1K2lRV0obivoNjlPbaT
        LAdL3mc2XAIjp/jH3+KDKoAvP6qrvnkD6uyMBRSfj0gN7w17Sqs2gfEWfTF28YMNa73Ba7Ix8Mo3
        iqniHwLgn9srdZfXuZ9WPapZ2iATvjI/QX9IQacqZ4SIKry3weQvxgGkM50RfwkoIDt5OYBScfy/
        fGGPx4MDQWuGWP9YUzFEV3gA8Xm40QheEZhGe1fBs5q01AnwwIZav9uQLO8vkWjYF/ODLql2dvGb
        3GjuxVRJ5ASufAYpiaKYC3jdBLi7vrbUTWnXM/tohTA2hIbuOP5bzGEHDvB6GRRs3M6fmowwa1Sx
        y3oiR/3Mlsrz71AnL58+snnq+/zbEdULHiHiVr05w/gLgTZZPTXCrRy/7pZnwKNdemv8eMCG+JVC
        yckTGmjYZMvW4HMYfHsP+7aLzPbYagM4eGZO/WazFPP+fengWk9o6hK/JqMxXbV87yp0/832wXS6
        Tx1Y+S/G77Hs2TjWL1C/ook8fn7BGxUqjGvLw9s5PcRTqLQH0Jr99k8vr3pzgUW3magBDdovwdWO
        gBe/OGwpwbZe7Mslh/f9urS5IwgIXIpFIBzthghGt2Fj5Hyuv/wik3t7xuMN3ezf9f/6AVv81iHg
        8XYNwiGFsh9fgeUHd9Q44tScQl13oX1hy9p/JDAa70AASVv0SKP5t2CahCxoHcYD2oATBotkuDZc
        9R/aZJ1rMlIYiRpWhy09DnA2JyUELlj7CzZpXLIPAc/8h1fYJ7uhZ/lklPA6XY9E4e5J3SbSPAAV
        eCk2wm0WM+RqAnAUd0uT8YaLxZqvkepZfIa9x2VfrP7CBIXHtUXf0yj1pPbdDqLA0bH33dUx+WQc
        B4tOmrBf81I8748LAmQj1UR9AK7u94kUwVOUa6Re/ZHv4xqp2rO2r2TeUrkYWxJMMIzbAgeweBfT
        9WqqILj2JRmDp9nPp1sQAVbhG3kCozFHM+pesN8lBeoT69OzlW/DufraSJjiLp5sTAQYjTQgqt80
        AWU3HsEPu9xxcERDzw7mFcFrvN/hVb/EzIJNCaS96uLdUFzjyWiUP71DAP9p+nlJohQK+WvGIX9Z
        zDkiJIGnUydR54r44EXinQ9Ztb8hmWViwL532gDxvW78ON0jYNE82+rOH550m4wWoFF/PMBzcHvQ
        re6AmAh6MEDHMTjy6/8zeNQZqK8+95fvX2hXyQ9/qG8OS0EVlJVQzpyK+ktt9ezbfxAA3gSRRJYh
        mFb+DFQ4x396njpcp/70Fdaf4lKzZ3n3AX0Nwx++zyxXXTWS7ioCuMEFi+6XFqiSciULU2+MiOtG
        q1hFKtW7pOuX+Y1CsPo5GOGRxEsJrBck9EQw5dVDzPh5uMNVv+DHWp/LJ+ATwE5YwEa5r/p24D5X
        0F1oSDZp29dLlxQViD+cTt2oP7IpR8iCzeN6olftfGKU78MONnlzp0bFv/ppy6sRYGjk6Y4LfMZG
        p1PB47Kk2O4nO2Y3q0Wwlsruzx+UCs3w4T3dptRS+NIcSX/WoUOziOKfXnG0XQkLzunIMaVPk0LT
        ecEs4xC2pvHb09dGgcCztIzi6tYUE/jIDXyPp4hu5c4sZmvvcrD5NA4a9ilXE9aEOWyM+0hk3nbj
        zU3VXlDkDxXetrJcDGD9BwtpNyqB5lEPhLV+RcwfexpWU1OzZ5xWUCXBiMRt8Q5mD6bJD98oPsRZ
        T/2m1eFPL+6XzuyZp2wjzWuEH99+Ft2Pb1dTPVFnEtp6kh9Jpqmvb4U2j7JibTIfZXApTJF0Aq8X
        LA6mHK56h7r5xQqEA+KucENqhagffOz//Lr6rY57DrrvYGGNlWs05zSqT7pej5qwNSC4GSKCJyGt
        p/iwCOB3Ph71RTFr4zPUVj8EzVuaxZNS5g0I7GDA+2eSxKTLWQqHiFEiiPTNupRsEqiYw0xt57aN
        x9lOSvi8oxf251PDRnI8L3CbBxyRjFlgxLhyLeA/9YFMKz9c9lOtwg14VmQR00tPrCPIYfy8hlR/
        GkIwdsb3Cm7mENBorgqz4/cXH6rJhce7OJuL5ajo9i++qHfTTfFd+xk0dmxPBrmWiuF2M2xYn2OH
        sGB69lQXDjoMLkVDhO1VDqYrrRNtxUeyBBpfMM7aLZAL7inVn2JUT0djY8DMSY9IQFlrrnzVV06K
        n1DrkL/65RufUjAHCsEuTW7FpCv5HXJueMU3+zsULFWdBp7jzxG16SkolC/dQq3V2gijkTsHU45s
        C3TgNdBdEk7mkt2iA5jfQCdqU8Tm/PMPFRv2f/qR/fyA4nWC9NfP591YGnCtV8TN7aGez3fvDlb+
        hN34MRVsTLoS3pPLhRqqcOwnQ58qkB0tnQZHFNaTwSYRHC+LSZRuLIv5ZIaGuq3BiB335rPlYl1C
        0OoXB7vKvS+IY08+nHHQUq8b9Vg63IoruNcGj3eLeSlYvDs0MCboiKrbbDPpfZVlyH+eB/KUD5Y5
        7z460n74wK40Boxe9Qp0Jd5iy09o3MfRIQRWd+cIa4pvTSwuliGIq56A88FlQ+tNdzjJvYA87SCC
        peU18efPEUmWt8WqN18APYWMpjY2ayFU2khZ8/HHb9kynI4DaKFxwoE892x03YcIxntXEk4tP+Ab
        g5FTFmcqf/Vbbzjkpn/9QjCtkzkLVeZCE+UW2RRGB9roC2Q1mBdADTG91EyQuDuMdieJ+tJcFZMU
        XSfw8nIf75sqCj6b5SqA6OG8Ef96VGAYgo/7w1O6PcS7fmZbxQLN437ClqcezIYbVB02TvrCzlsV
        2Lw9vnL4yx9L4fWgH+IhgabCy9iR5W28+VZGomU3cUeWRME182wdgq44e3TfcnsmHTW1gu1S+dRy
        057RH/9Y/Tbqrni4WfUOzIxrjsPXUptLZXQdfHvS5i+fysSacxD7RKDe7Xw02ZVzE/Xu990f3v34
        Gew09EXMjL51//v+FW/pVgcKGBGfydA3rg4O7EQGy5OaPjxl8kyD1f9d9t7bgPqxqei+132TSno+
        QK8fKN4SVaj7t6F0v3kQ9le+LpYnU4Uos78EkKNUzOru5ULlHCKcqiMMJr7GV5g97sZPjxeEt+dU
        Oz31mtzCk1FI+ifLoSRt+L9++DePWvXnOv9Ig3F6nBoIiJrSn984h9bygj8/bK33mG1kof35z6s/
        KgUkuKIDODwzSu2mcmsxg8wFhx0ryQtlbTDcDv4d7uEtx97Kz2b5Rg1oKpqMHamrCzqqPVIsk34o
        Cp97k52fqILQjwxqPrW5ryyuUCWVeCMZ/XgHJn1R25/eIdwkROZUXR6uGuBeIrLYTvHw+iTwpzep
        ddDf8cSljgB3Pnni4JCTYO0PFVz9YexsNTGmu3q8gq0UfPB28RGbtY+Xwx+/E9guLDYCmoZfv6SB
        fB76qWb3DJwSXV3jfy7mn1+k26GItyL69OxMkA9//tkPT8dPYpVQg8JEbf6p9otan9uf/0wup/Fc
        z89alqFdP58I7F9xMD2OegvX+RACKJv7zzNO//CFGnh/qZleTg08u/s33WUHlwnNk6XaqWrLtb4D
        Rn9+mmDcdIr5cg7oqvdA4yQvjPzdHM9zA14AdMaDBiprg+GAuDtc/cgVrxK2/n5XXZylJPPCjTEb
        4iEFP/8yjLiiZrpwMDQfo5AG/abop5bOvrbWO5JPYAjmMIhy8Lue+eRYpvDjS6tfsG6EIvDnT2jz
        0FJTt924MURNhsnmkpAoMpN+PnNtBY/bLkJCKpbm6t+n0K7rJ5q0nq8pd34jaD12Ef7LF7mcJ823
        bIsc13jTXhkNSKF4w/rbHtgcgzeEt0mBSGrTQz+Mb5LDbP+q8Pb0NP+Np6NugNX/+wSj2fgGCFP1
        tJ5fDzbvGCUiComGGC+obDyl/RXqT3FHeN6eGQnpm0CUWV9qniUYT0H0zLX42iTUJLs6aNpFDGFX
        7reIJPz7N4/M4Lb8MowA2fVLPXmp+ps3r/NesPDxxEFnXE7Y5e02pvsnJ4NToLgYn7UZrP5tohk3
        lSNLxb9WvNYMePe/HbZN9gGL8RUPEGy/AbW21ywY06kjf/Vk+QTVY2DC7IffSGzgB7DNkgjapY8v
        RHiebr/8z1T6bWrsvG0xXvWO/NPH2Aymbb/5zV/6h+3h7dkr4zJHtg0Mb+uQDf1o9fjUJh+epOqM
        5uj4NZfHc5dC56kf1v71NGfjbQrqii/4hzfzoi/qr5+Tlb+ZSyjuSrCkuU3R2+bZ9604OhyG+ka4
        Ns2DVW9a8ApeF+yTRwB+9QbEPHAJB6tX8Z2GUwjXeS9FgLzr6XoNVCh5xvFvXk+cYRLBz7/0yS7s
        ZzU0uf+9UfCPf/7zf/6eWdC0t/t7XQwY7/P4H/9nVeA/8lv+H4Ig/j3YgAx5ef/Xf/57A+Ff375t
        vuP/GtvX/TP86z//Kf6tGvxrbMf8/X+9/Y/1RP/1j/8GAAD//wMAjOe+STBBAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c08cd2171e-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:51 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=5OrCIMrDXJuaI7E_fumscwCdlgwf.KELcNNXpqUhEow-**********-*******-bNxmXGmOqEyX1SWd6RncfX6UbfadPSUR6nozzYuIFPlH.ZyTJa3ShucPXS_IcZgJ4_DUw9_D4zpH1AZ488gU2g;
        path=/; expires=Thu, 11-Apr-24 22:22:51 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=4lJZBUnx2WP1QxsOjyiKsxhmmCLce_ojBS66oRdC3kU-**********411-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - text-embedding-ada-002
      openai-organization:
      - langchain
      openai-processing-ms:
      - '22'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9996'
      x-ratelimit-remaining-tokens:
      - '9999998'
      x-ratelimit-reset-requests:
      - 23ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_38eadc2dd736cb2e124bf1a9b9d456bd
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a car'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a sedan?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: Yes\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3xTXWvbQBB8169Y7ll2bSeta7+GNIRCH4opmLqY02klXS3tKbcrXBP838tK/ogb
        6Ivgdndm5+ZGrwmA8blZgnGVFde09WjxsM/LSVk+fn15Lr/8ePhmH7vse/b0VHf2YFJFhOw3Ojmj
        xi40bY3iAw1tF9EKKut0Pp19ns/u5rO+0YQca4WVrYzuR5NP07sTogreIZsl/EwAAF77r2qjHP+Y
        JUzSc6VBZluiWV6GAEwMtVaMZfYslsSk16YLJEi93FWF4KIXjD4QFCGCVJ5BLO/AM3SMRVcTMqew
        r7yrtJhj4QlzsAxSIbQRc+/0spChpxJciBGdgKX8Q4hgeadVeyKDItR12HctvHTIChtvaEPPNGx2
        ljHtebnLGs+svJ5hY9bIG3OW0VhxFQ4CIhYYkRyCJd5jHMNKmRq0pANW/pWpawaNOnqzKQ/IQEFU
        Ndj3WlPIOhmU+mGQ0OkDxEPvXoMoelu5+mrPRl3PSsr9PHqpMIK9mPZGpnr3f9NWFUYsQnzvmOq4
        3doD1htam1MQjpcE1aFsY8g0bdTV9aVeePJcbSNaDqRpYQntAD8mAL/6pHY34TNtDE0rWwk7JCWc
        Te4HPnP9Ka7dxcdTU4LY+g1qsUhOCg0fWLDZFp5KjG30fXBVZ3JM/gIAAP//AwDVv4w5rwMAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c84831cf45-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:56 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=_syfr2WeOGqh4FqMjdRC_PyiPJJmytQaW.sG5WImOdk-1712872376-*******-x2S4qN84_7F0gwkvLqa5NEI7.ZZ.64w0QVDjWI_G5lJvvXsQHTeJHDimKd_Dcaq60svgo53hO9JJ.YkoRqAd7w;
        path=/; expires=Thu, 11-Apr-24 22:22:56 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=bYWKw.k39cbtUhvvU7LCsaJlz8a_rASbTSogsEAWDgI-1712872376901-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '4275'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '299759'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 48ms
      x-request-id:
      - req_e7f462a60f4b7e6f113754c217415d28
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a car'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a jeep?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: Yes\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA2xTTU8bMRC951eMfMkloWygDc2lBy5F4ohKo4KQ453dNXhntp5ZhRTlv1f2bggI
        LpY14/fh5/HLBMD40qzAuMaqa7sw/365Leur/va6LuTi39efRXn9uPl9g0X8dXVrZgnBm0d0ekCd
        OG67gOqZhraLaBUTa7EsFhfLxdlykRstlxgSrO50fj4//VacjYiGvUMxK/gzAQB4yWvyRiU+mxWc
        zg6VFkVsjWb1egjARA6pYqyIF7WkZnZsOiZFynZvGgQXvWL0TFBxBG28gFp5Ai/QC1Z9IBSZwbbx
        rknFEitPWIIV0Aahi1h6ly4LG/RUg+MY0SlYKr9wBCtPqWpHMqg4BN72HfztURLs5I7u6Jp5OKaZ
        1FPX6yxvs9tnTcrTZLe1lPbECqKIcSB3Nk6TYoYcmDPm0hJsEQLX3tkQdonQhb7EfF3pI4I2o+zn
        3I+I3Y9ptpkMSL9pvWpKgGSLMausUaaHjFqrrsEhnYgVRiSH4+ETuEkBt2hJjrpvMvRyCPC9oEjq
        loyDvfRA9mOWM9j0OrzheA9ClwYk7j55r49aESuOOAT/RrZF1AF9HBau3oxHxq/vaG3GMdu/zmfg
        uou8SbNMfQiv9cqTl+YhohWmNIui3A3w/QTgPv+D/t1omy5y2+mD8hNSIlycng985vjljt2iuBi7
        ymrDsXG2WExGi0Z2otg+VJ5qjF30+V8ko5P95D8AAAD//wMAUoMU+w4EAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c84a7b17ea-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:57 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=ER5kCl2QwvB_Y2ucvpkG05atInnH6AjdgOjLgc8xokQ-1712872377-*******-rVbXxP4nlWdwi3njgiKLffIYeHNimi5.hCeFRfTUlDaKQ4968gUp3Mt3a3EUNfibsTjo2qn6rymT0YO.a3soUQ;
        path=/; expires=Thu, 11-Apr-24 22:22:57 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=SkX7F6FLp8qjOYESeaPfJpl4I6ZRHKY9Xwq1WqiRYok-1712872377489-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '4856'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9998'
      x-ratelimit-remaining-tokens:
      - '299518'
      x-ratelimit-reset-requests:
      - 11ms
      x-ratelimit-reset-tokens:
      - 96ms
      x-request-id:
      - req_648bb00b6435e233b39fb4198b35cb96
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a car'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a convertible?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: Yes\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3xTTW/aQBC98ytGezaUjyikHBv1QFtVqsShpERoWY/tLfaMszMWVFH+e7XYmFCk
        XnyYmfexT8+vAwDjU7MA4wqrrqrL4cfHQ5rb1Y+fX5dPR3qc1fm6+Xz/afnl+9O3o0kigne/0ekZ
        NXJc1SWqZ2rXLqBVjKyT+WT6MJ/O5tPTouIUywjLax3eDcf3k1mHKNg7FLOAXwMAgNfTN3qjFI9m
        AePkPKlQxOZoFv0RgAlcxomxIl7UkprksnRMinSyuyoQXPCKwTNBxgG08AJqZQ9eoBHMmpJQJIFD
        4V0RhylmnjAFK6AFQh0w9S4+FnboKQfHIaBTsJR+4ABW9nFqOzLIuCz50NTw0qBE2GhDG1pSq+ys
        YHLilWZXeZHI6wU2Zo2yMWcblVVXYGsgYIYBySFYkgOGEawiU4WW4oHVf21GmdbjCKL26lotZRQg
        1ugc7K3fBHaNgvioqFf52XgpXYxXksqwQ0CvBYY+oDac/ySTtJn4zg4EfGl8wApJe5F3xpWhQtT3
        puyoe2DAjMNtsvFebgHrDa1NV5i3vmkl53XgXWwlNWXZzzNPXoptQCtMsVWiXLfwtwHA86nRzVVJ
        TR24qnWrvEeKhNPxXctnLj/PZTvpt8pqy8tiNn4YdBaN/BHFapt5yjHUwZ8aHo0O3gZ/AQAA//8D
        AC63my3YAwAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c8ff0c67c1-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:57 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=uBgvN1hX_VJANk4lHzKX.WoO0Rj0YJtGRXb_vazGBf8-1712872377-*******-Zap6pz5_HJl.iiXmFuzj_bFFNBfIPR4Nfvc7kKqdAfrOadJ6fmeqq0C4IOXQ5o8kzN5wZZd.XqGXKsU9wHv.kg;
        path=/; expires=Thu, 11-Apr-24 22:22:57 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=VeIKfAPWTbksncZfm.gYlseahryqR4izpvBJftUHsbc-1712872377878-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '5140'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '299406'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 118ms
      x-request-id:
      - req_cb91564c57dcc729944a2c358f37e6bd
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a car'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a SUV?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: Yes\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3yUTU/bQBCG7/kVoz0HShIgkCNckFq1lWiRUFOhzXrWHrLeNTvjGoT479XYCWko
        7SWy5uPdZ77yPAIwVJgFGFdZcXUTDs4vu6K8vA9XX/18ffPlc3d2fdPJxfrkYxQyY81Iq3t0ss06
        dKluAgqlOLhdRiuoqpP5ZHo2n87m095RpwKDppWNHBwfHJ1OZpuMKpFDNgv4MQIAeO5/lS0W+GgW
        cDTeWmpktiWaxWsQgMkpqMVYZmKxUcx453QpCsYe91uF4DIJZkoRfMogFTGI5TUQQ8vo2xCReQxd
        Ra5SY4GeIhZgGaRCaDIW5LRYWCHFElzKGZ2AjcWHlMHyWq12IwY+hZC6toGHFlnTDpdxGT+lNIRJ
        L0qxaWXcf/a0jwIsVlCftALLHry2UXliUidiHp5xNi/NIWjA9gVlYCAPHYKzEUIqydkQnlTchbbA
        vnRuMw768i/1CNffb3pg1ed2VZOI9iJyh1njl+YWeWm2/aqtuAqHTmX0mDE63IQrJDFQLMjtanvT
        U+JtQ8facRKwgcrI0JFU+7I911Xq8BfmoXc9ILPqFAmHYnS09p0pwEUrwKR4srcVViN5sxx7bJJg
        hdu5klc4YkCSCvPrGgwrwP9ZgIE1WLeG5N9j28EXxA+tDeSf3tbnc6qhRhSd014B23Fl9CnjX43R
        HH5TcvJ/rH6ff7uMt2ZzQi+vtxdS2eS00juNbQivdk+RuLrLaDlFvTOW1AzpLyOAn/2Nt3tna5qc
        6kbuJK0xquD06HjQM7u/k513cnK+8UoSG3aO2elstEE0/MSC9Z2nWGJuMvU3r6Cjl9FvAAAA//8D
        ADVmmy7qBAAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c98fa8fada-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:58 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=U68_p30JFeIm5Owoo90aP0.kj2ohG.1.oIlb3PLoP4Y-1712872378-*******-CC7vzfdWYR8N8Y1JZWDhkjWdAW96wsgoCAiDXupGcGR9VVbiqTt3t.8ihn6FJDy4ZaJZP7hmBzQa3SXDuE_AzQ;
        path=/; expires=Thu, 11-Apr-24 22:22:58 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=fWTJWvhn9kk_Uws5fx.5UuC6ia_OBz2KRjT13I60iwM-1712872378694-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '5645'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '298818'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 236ms
      x-request-id:
      - req_d6f490eef32a3a1ea0e772b01490a76f
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a car'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a subcompact?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: Yes\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3RU0W7TUAx971dY9wWQutJ2Gxt9BCEmBEIae5komm5vnMbdzXV27dBN0/4dOU3b
        jcJLFPn4HB/bcR4HAI4KNwMXKq+hbuLR+4/rYrn6dlVdyOWn1Sr51Ze8/jwuflx+H1+5oTF4scKg
        W9YocN1EVOK0gUNGr2iqk7PJ9Pxsenw27YCaC4xGWzZ6dHI0fjc57hkVU0BxM/g5AAB47J7mLRV4
        72YwHm4jNYr4JbrZLgnAZY4WcV6ERH1SN9yDgZNi6uxeVQghk2ImTlByBq1IQL3cAgm0gmUbE4oM
        YV1RqCxYYEkJC/ACWiE0GQsK1iwskNISAueMQcGn4i1n8HJrUd+LQckx8rpt4K5FMdponubpK/Mm
        TTtRSk2rw+61c3uvIOoVraRXmHfGa5/MT2IDEfOmTPB57kZgCdsK5kGASlgjBJ8g8pKCj/HBxENs
        C+xalzbjRl//qy7twrbrg47AfFsZaRc1iRCnVwI+yRqzMefuGmXutpOrvYYKNzPLWGLGFLBPN7sk
        QKmgsO9SX2ibZD/abmIXvMbfmId/pxWMG9e2Q/+PccOHVkHIquuL9e+YGe9ayggL1spGVJARBV4f
        LHi/3YM6b0AZFgg16oFHW4IoxWgJgZNQgRmL7SdCJZA+63f38Vlw1I89Y8kZD6RrRJXnjfmOcD1P
        164/gafd7UReNpkXdmepjXEXLymRVDcZvXCyOxHlZkN/GgD86m60fXF2rslcN3qjfIvJBKfj042e
        2/8O9ujk5LxHldXHPXB8ejzoLTp5EMX6pqS0xNxk6m7WjA6eBn8AAAD//wMAeqqMQaoEAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c90a2cfa8e-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:52:59 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=AuLtIqw3koeAeosLE7yWn5UuBhwaU5jDvQYncv2C5dE-1712872379-*******-F3anOuTMtC1hS_A3FJsGVc6ml4OKyolXnIe9qdv_arHnvvxCl62P8U6NKMy2KoZCvML3FasqtMgYQMAFzYVPyg;
        path=/; expires=Thu, 11-Apr-24 22:22:59 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=8eAZ5kbDu9yMlIJrpQ4ITsevmTBX2nJczh_2Qy9R0Co-1712872379704-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '6964'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9997'
      x-ratelimit-remaining-tokens:
      - '298948'
      x-ratelimit-reset-requests:
      - 12ms
      x-ratelimit-reset-tokens:
      - 210ms
      x-request-id:
      - req_d8fac4c146c75db38073a635359e440d
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a sedan'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a car?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: No\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA4RUTU/cMBC9768Y+dLLsmUXBJQbqlShHpBaUaSqWyGvPUkMzkzqmXRBFf+9spP9
        6oe45DAz7/m9+civCYAJ3lyCcY1V13bx6N37ta8/tKjiP1/XH5uHh/Wnk/7uzrdfrpyZZgSvHtDp
        BjVz3HYRNTANaZfQKmbW+fl8cXG+ODlflETLHmOG1Z0enR4dn81PRkTDwaGYS/g2AQD4Vb5ZG3l8
        MpdwPN1EWhSxNZrLbRGASRxzxFiRIGpJzXSXdEyKVOTeNgguBcUUmKDiBNoEAbXyCEGgF6z6SCgy
        hXUTXJODHqtA6MEKaIPQJfTBZbOwwkA1OE4JnYIl/5YTWHnMUTuSQcUx8rrv4EePkmGzJS0pCwnU
        9ZpfsBC5Ds7Gbc2GJVSwRnCWwDG52HssoqVPCNpYLYpaS5mFWEEUMQ3PO5umUIefSKWoNOFJX0MJ
        eksz2EiUftUG1eyeZI0pI5bmK8rS7HUo0NiDGVwNDIMrfe4QuBqkCGc3r7w8haDgGYXeKBC6POsU
        4jO0aAlyz/7lcwbX2WAfPaxwP+dDVWFC0n0pM7htMGHFCad/TnSk39g5aINIGUwULgpLYd6b/096
        MK278hZxaL6gY/LQ2aRZlR6sJQZtMG0fT1g8ODyYwQ0fjGAY8WYRS5lyiW7XDm7zqld9yux5H6qQ
        WtktxJ7Jg5mOOvY7tlf6t7fRiC3AmyXdmPEUX7Y3HLnuEq/yvVMf4zZeBQrS3Ce0wpTvVZS7Af4y
        Afhe/hX9wfmbLnHb6b3yI1ImXByfDnxm91vaZecXizGrrDbuEicXZ5NRopFnUWzvq0A1pi6F8u/I
        Qicvk98AAAD//wMAOmtnyjIFAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c9cc0ffa6a-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:53:00 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=ork2miUx0hShbPC0wyHSBcXH3gGl1BoSEH94qTDU_kI-1712872380-*******-R1xbuKhNwhK4Pu5L5hkY2bz1AI25jW_Tz9NO1lqpo556lklrIe72oLhNzqTDrgDd4wCCzEP8Y7yTnqD1jjWW2Q;
        path=/; expires=Thu, 11-Apr-24 22:23:00 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=B4KOjMeF5NjJT68S2KiOzpJ0V4RiYCEDdLU8Q1gxXVY-1712872380376-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '7488'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '298899'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 220ms
      x-request-id:
      - req_bd5ce83e3a3a782e956775851e4e1533
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a jeep.'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a car?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: No\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3yUTW/bMAyG7/kVhM5u1iTF2uYyDD1sh67AuqLDsAyFLNO2Wll0RTpOUPS/D5Kd
        pNu6XgyDH68eSiSfJgDKFmoJytRaTNO6o/OLvqiuP62/3F5/NbffHz/P8upcLrf22yI8qixmUH6P
        RnZZU0NN61As+cFtAmrBqDo7nc3PTueL03lyNFSgi2lVK0cnR8fvZ4sxoyZrkNUSfk4AAJ7SN7L5
        AjdqCcfZztIgs65QLfdBACqQixalmS2L9qKyg9OQF/QJ96ZGMMEKBkseSgogtWUQzQ9gGTrGsnMe
        mTPoa2vqaCywtB4L0AxSI7QBC2tisZCj9RUYCgGNgPbFOwqg+SFa9SgGJTlHfdfCY4cc06Yrv/KX
        REOYJFHr206y9JtoNwIsWjAeqQVWCbzRPvJ4ik7EMBxzj9hOVwpixO6ICMEZrNSF9tAjOKqs0c5t
        o7pxXYGpdu4CDgfIf+WNDh9G9UOh3GMAoeHydodm8YYqu0YP1ifFgCUG9AazKLxSV7RSU7iJSZYh
        R6M7xtfqgYJwIPFo4nsH67bQoPZvk04Tp/QEOgzCTSddKhw3xnVs15iuP4ZxlzdWJL5sKiiDmnpc
        x59E+wP5Ja71u/pzzVgADSzpagE3rdNDl+S0xsQRsKSA2d9NM4KPYonmY1HY6IugQ3xiY47x+6uI
        Par/bads12gUgKTG0FvGKYxVvoR4TbRBHF7/MBVUvpiD+KZWEjTaqL7vAk9hkLE7tDcb/mrlr9Q4
        k8/7YXZUtYHyOPi+c25vL623XN8F1Ew+Di4LtUP68wTgV1oa3R97QLWBmlbuhB7QR8H58cmgpw77
        6eCdnY0rRQmJdgfH4uxkMiIq3rJgc1daX2Fog01LJIJOnie/AQAA//8DANvXF107BQAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c90d1e7aca-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:53:00 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=kT_9.zCx2vcv8yWJc2whc8YfUJc7heIe9ud6otSJ6Ns-1712872380-*******-TBIHTiko1UBuvE3mfIqcbK84p3lVH7ywvvnCITojtVicQS_Yai3BkS_mm.ByIEJURPDYbtZcbq6jYVecwuxMZw;
        path=/; expires=Thu, 11-Apr-24 22:23:00 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=yKWEmm7IjfqDWJ38jHgqmyKijPNTaVbVlhcjDTA5vT4-1712872380904-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '8158'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9996'
      x-ratelimit-remaining-tokens:
      - '299119'
      x-ratelimit-reset-requests:
      - 21ms
      x-ratelimit-reset-tokens:
      - 176ms
      x-request-id:
      - req_ff43ca0dffccedb9c360ac383de4755f
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a SUV'', ''question'':
      ''Can we logically conclude for sure that the man is not steering a car?''}\n***\n[Submission]:
      Yes\n***\n[Criteria]: usefulness: The prediction is useful if it is correct
      and/or asks a useful followup question.\n***\n[Reference]: No\n***\n[END DATA]\nDoes
      the submission meet the Criteria? First, write out in a step by step manner
      your reasoning about each criterion to be sure that your conclusion is correct.
      Avoid simply stating the correct answers at the outset. Then print only the
      single character \"Y\" or \"N\" (without quotes or punctuation) on its own line
      corresponding to the correct answer of whether the submission meets all criteria.
      At the end, repeat just the letter again by itself on a new line."}], "model":
      "gpt-4", "n": 1, "seed": 42, "stream": false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA6RUwW7bMAy95ysIndOuSYqly2UYusOAAb10WzcsQ6HItK1VJl2RrlsU/fdBspMs
        XXvaxYD59B4fKVKPEwDjC7MC42qrrmnD0bvzvqjOi/7DZfxOcdPb5aJdXj18vj2rrz6aaWLw5jc6
        3bKOHTdtQPVMA+wiWsWkOlvO5mfL+WI5z0DDBYZEq1o9Oj06eTtbjIyavUMxK/g5AQB4zN/kjQq8
        Nys4mW4jDYrYCs1qdwjARA4pYqyIF7WkZroHHZMiZbtfagQXvWL0TFByBK29gFq5AS/QCZZdIBSZ
        Ql97V6dggaUnLMAKaI3QRiy8S8XCBj1V4DhGdAqWijccwcpNitpRDEoOgfuuhdsOJdGOYU1rSk48
        tZ1CG/nOFyhgITu9z1Jg/yJk2yPmBda5jsZS+iFWEEWMQ9bLr9/WJgsks1uJgXVuCXqEwJV3NoSH
        pOlCV2DuhHQRQWurmfmyurPx/drsCpBu03jV1BySHuOQ5gfK2mwbKF1VoagMyj2Cs/SfDmBjBQtg
        yscqf4e07c7Q20/c4x3GacYjlhiRHB54vOC9xcSNNl3qcMPPq0r99/KsEn2lfE/jPBy/3iUbhKFg
        HIpLs/f6tGxNNmhJwOue1yCONtAxFdDaqMBlDu1mfOciYskRp4e9268ClwfD/2J1/2Ye+XbIcrGm
        CzOu3dNuXwNXbeRN2m3qQtjFS09e6uuIVpjSbopyO9CfJgC/8rvQHay6aSM3rV4r3yAlwfnJ6aBn
        9k/QHp0tt6iy2rAHFsuzyWjRyIMoNtelpwpjG31+J5LRydPkDwAAAP//AwAJJr7THgUAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c8dfe6230e-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:53:01 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=pF1oEoYT_biORIRg8npH9A2.lKMM2.xf.nn7zeHZ6uo-1712872381-*******-Sk3.VxqWZU17oDw1mje9SnxPZbjFyhJrl2f4XbybPby9vRp6.sgtaykCodfK.XVVXuBNkKPGUOKwhKKkSxIWYg;
        path=/; expires=Thu, 11-Apr-24 22:23:01 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=NFO4KfBNR4Pi53icCxsLgA__JxG86cCzHbkUNCAjxdE-1712872381241-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '8511'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '299508'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 98ms
      x-request-id:
      - req_16709a33494e9fdd5546d9d63f9d6d40
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a subcompact'',
      ''question'': ''Can we logically conclude for sure that the man is not steering
      a car?''}\n***\n[Submission]: Yes\n***\n[Criteria]: usefulness: The prediction
      is useful if it is correct and/or asks a useful followup question.\n***\n[Reference]:
      No\n***\n[END DATA]\nDoes the submission meet the Criteria? First, write out
      in a step by step manner your reasoning about each criterion to be sure that
      your conclusion is correct. Avoid simply stating the correct answers at the
      outset. Then print only the single character \"Y\" or \"N\" (without quotes
      or punctuation) on its own line corresponding to the correct answer of whether
      the submission meets all criteria. At the end, repeat just the letter again
      by itself on a new line."}], "model": "gpt-4", "n": 1, "seed": 42, "stream":
      false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA6RU32/TQAx+719h3XNX2m5s0Bc0JiR+aTCEGIig6Xpxklsvdjhf1nXT/nd0l7bp
        pAIPe4l0tr/Pn+3Y9wMAZXM1A2UqHUzduIOXZ8u8vPz+/vTj5fjN4afjBeEHvrs4vfty8fmbGkYE
        z6/RhA1qZLhuHAbL1LmNRx0wsk5OJtMXJ9PDk2ly1Jyji7CyCQdHB+PjyeEaUbE1KGoGPwcAAPfp
        G7VRjrdqBuPhxlKjiC5RzbZBAMqzixalRawETUENe6dhCkhJ7tcKwXgb0FsmKNhDqKxA0LIAK9AK
        Fq0jFBnCsrKmisYcC0uYgxYIFULjMbcmFgtztFSCYe/RBNCUP2MPWhbRqtdkULBzvGwb+N2iRNgo
        o4yiEEtNG6DxfGNzFNCQhN4mJtB9PCTVa58VyFIZtab4IA4gAdF3SaWdx2loEzKVeKLkDVMHPtME
        SwTHpTXauVWkNq7NMfVDWo8QKh0Scn8So/2rTG3rkHZe2xBii0iW6Ls0P1AytW1j3TiL0hEvEYym
        JwqAuRbMgSmFlfYGadOjJOwtL/EG/TC5PRbokQw+UnjOmYrNtQLSliVKeCQw5nyixutWAszR6FYQ
        4sT/Ma8RnO68YqiGsGoQuIhcQxD+Oxhyxo6b0MQF8datoEZN+2TpANq5zfw8Fuyxa9S+UVra/uEC
        didVrYOp9vZ3BO9iCuE+OG7Yf3bidT/RWD1ptxIrO8JE4k/c50fsBtBvNBc7O5xIzzM6V+tb8LA9
        Io7LxvM8HhxqndvaC0tWqiuPWpjiwZDATQd/GAD8SseqfXR/VOO5bsJV4AVSJJyOn3d8qr+L+7yB
        g3a942gyHqwlKllJwPqqsFSib7xNxysKHTwM/gAAAP//AwABx9QGswUAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c86b41cea8-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:53:01 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=EjjZ6swOGIec.kYPdg8ZPextPq.N1qH7ivgpBRJzzaA-1712872381-*******-KAwilzyUeNnZ24dTfiHlDfI.qneKsJWrDfDnX9hFEjh5tA33nTSYL4qHUdk3kh5JtI2CDusx_AcZyfwek3q0EA;
        path=/; expires=Thu, 11-Apr-24 22:23:01 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=Y9yA2wjAcZbIM3GL22xjFCS56KiVvRDd.RKqqpcnE6Y-1712872381873-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '9215'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '299413'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 117ms
      x-request-id:
      - req_7339de803d169dfde5b473533a8e38f2
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": "You are assessing a submitted
      answer on a given task or input based on a set of criteria. Here is the data:\n[BEGIN
      DATA]\n***\n[Input]: {''context'': ''The man is not steering a convertible'',
      ''question'': ''Can we logically conclude for sure that the man is not steering
      a car?''}\n***\n[Submission]: Yes\n***\n[Criteria]: usefulness: The prediction
      is useful if it is correct and/or asks a useful followup question.\n***\n[Reference]:
      No\n***\n[END DATA]\nDoes the submission meet the Criteria? First, write out
      in a step by step manner your reasoning about each criterion to be sure that
      your conclusion is correct. Avoid simply stating the correct answers at the
      outset. Then print only the single character \"Y\" or \"N\" (without quotes
      or punctuation) on its own line corresponding to the correct answer of whether
      the submission meets all criteria. At the end, repeat just the letter again
      by itself on a new line."}], "model": "gpt-4", "n": 1, "seed": 42, "stream":
      false, "temperature": 0.0}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA6RUTW/bMAy951cQOqdZkmbrmsswFEN3yi4Dgm0eAlmmbTWy6Il006Dofx8k53NF
        twG9BAif3+Pjk8THAYCyhZqDMrUW07Tu4vpmU1T8aZYvl9eb29tq/P3Lckvrt83dcnGrhpFB+R0a
        2bNGhprWoVjyPWwCasGoOrmaTN9fTS+vpgloqEAXaVUrF7OL8bvJ5Y5RkzXIag4/BgAAj+k3evMF
        Pqg5jIf7SoPMukI1P3wEoAK5WFGa2bJoL2p4BA15QZ/sfq0RTLCCwZKHkgJIbRlE8xosQ8dYds4j
        8xA2tTV1LBZYWo8FaAapEdqAhTVxWMjR+goMhYBGQPviDQXQvI5VvRODkpyjTdfCrw450kaZz3w0
        Yn3bCbSB7m2BDBqS0YekBPr4PSTXO8wyZGmMRvv4x5MAC2Lomxry9xjE5g4zlYSi571Uz77RHjYI
        jiprtHPbSDKuKzAFwl1AkFpLYr7QRYcPmToMwl3eWJGYkecNhr7NN+RM7XNsUPtn+cXkY6MNgtH+
        lYYObgKWGNAbPHOzoOdmDq2j2uu6w13HAjka3TFCPNy/HU3yekNNqxMm+wyZYyxCqXKYYwg2nbtx
        qMPRywnDMli/u4cj+HjaKmIaZNsiUBmdDoHp3K38z22CgrDHY3ovTKjDCD7Hu9q5AnI8xQpbpnHk
        1Mv5Depn0Y7p2Cw+zH8/pYAlBRz+GcuJZewzO759Kk9ee9JZZH6hdlvj6bBuHFVtoDyuJt85d6iX
        1luuVwE1k4+rhYXanv40APiZ1lp3tqlUG6hpZSW0Rh8Fp+NZr6eOG/QEneyWnhIS7Y7AbDIb7Cwq
        3rJgsyqtrzC0waY1F40Onga/AQAA//8DAMHDaRbdBQAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 872e34c95bd167bf-SJC
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 11 Apr 2024 21:53:03 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=vXVspJxjv7dG_kCoW0uEt_NutH_3fgip.axpdjGUET0-1712872383-*******-C9b_Msm4ykgJVsBWjTR52EyBZmOUvS.oTg3nbd6HDCCUuhnaqu5XggMqlgqBH_MZIxrnMZGPZM53FTBfb8_QLg;
        path=/; expires=Thu, 11-Apr-24 22:23:03 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=jPm.coApYy2f9whIB1aUJxYT_aW50Xb6SbOn.5Q7tuE-1712872383220-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-4-0613
      openai-organization:
      - langchain
      openai-processing-ms:
      - '10445'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '300000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '298925'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 214ms
      x-request-id:
      - req_638318e0893bfa0ea4a8c852b03809d7
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"content": "Is the output accurate with respect to the expected
      output? Y/N\nOutput: Yes\nExpected: Yes", "role": "user"}], "model": "gpt-4o",
      "n": 1, "parallel_tool_calls": false, "stream": false, "temperature": 0.7, "tool_choice":
      {"type": "function", "function": {"name": "reference_accuracy"}}, "tools": [{"type":
      "function", "function": {"name": "reference_accuracy", "description": "Whether
      the output is accurate with respect to the expected output.", "parameters":
      {"type": "object", "properties": {"score": {"type": "string", "enum": ["Y",
      "N"], "description": "The score for the evaluation, one of Y, N."}}, "required":
      ["score"]}}}]}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA4xT24rbMBB991eIeY6Lk9i7id8W2i2UQi+79EKzGFkeO9rIkpBk2hDy70V21nLS
        LNQPQsyZOWfmaHyICAFeQU6AbaljrRbx3ZfvdbYwv5VdZ/f7t99Wzw+Szh/LDz9c9xlmvkKVz8jc
        S9Ubplot0HElB5gZpA496/x2maxWWZoue6BVFQpf1mgXpypeJIs0TlZxcnMq3CrO0EJOfkWEEHLo
        T9+irPAP5CSZvURatJY2CPmYRAgYJXwEqLXcOiodzALIlHQofdeyE2ICOKVEwagQQXj4DpN78IkK
        Ubzj6/tPXU0f3dcHtXxf3u0+ljfNfjfRG6j3um+o7iQb/ZngYzy/ECMEJG37WoM1GpQMC8pYZyjb
        X7AQAtQ0XYvS+QngsAHLlMEN5Bv4uYEjnKUfo2v3p4khBuvOUnFy6hQ/jtYL1WijSnvhJNRccrst
        DFLbTwTWKT1oe51eAbqzVwNtVKtd4dQOpSdc3w50EHYqgNkJc8pREcLzZDG7wlZU6CjvH3XcI0bZ
        FqtQGvaJdhVXEyCazPxvM9e4h7m5bP6HPgCMoXZYFdpgxdn5wCHNoP/jXksbPe4bBru3Dtui5rJB
        ow3vlx5qXaQZq7O0QooQHaO/AAAA//8DAERRdCH9AwAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8de411052c0a2320-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Wed, 06 Nov 2024 09:30:43 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=46aW_1z3Yu96PFQG.9WdGafKDLThRQ2V2BH4Wc__v2k-1730885443-*******-dyb20fuCsDK3FwOSUtTnfWmhmQbPJl2FAYfbORyKyICHL1woSnrX0KQ1fQOaD8ifq8ABkL0lDQdEb9zPCgTcMA;
        path=/; expires=Wed, 06-Nov-24 10:00:43 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=AQfLeHBIv_Vjs.O8aHH2.5xm6D8_TmJg6.A65iJorpk-1730885443709-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - langchain
      openai-processing-ms:
      - '223'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9998'
      x-ratelimit-remaining-tokens:
      - '29999959'
      x-ratelimit-reset-requests:
      - 11ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_737e0742debe3c7d3d8504412cf9fbe7
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"content": "Is the output accurate with respect to the expected
      output? Y/N\nOutput: Yes\nExpected: No", "role": "user"}], "model": "gpt-4o",
      "n": 1, "parallel_tool_calls": false, "stream": false, "temperature": 0.7, "tool_choice":
      {"type": "function", "function": {"name": "reference_accuracy"}}, "tools": [{"type":
      "function", "function": {"name": "reference_accuracy", "description": "Whether
      the output is accurate with respect to the expected output.", "parameters":
      {"type": "object", "properties": {"score": {"type": "string", "enum": ["Y",
      "N"], "description": "The score for the evaluation, one of Y, N."}}, "required":
      ["score"]}}}]}'
    headers: {}
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA4xTUW/TMBB+z6+w7rlFaZqwLm/VJiqKBgJtbIKiyLtcUoNjG9sRVFX/O0rSJWkp
        EnmwrPvuvu/u82UfMAYih5QBbrnHysjp8uNjkTyJeLn6NFNPb78sI3V3++4+2pkbfgOTpkI/fyf0
        L1WvUFdGkhdadTBa4p4a1tnVPFwskjiet0Clc5JNWWn8NNbTKIziabiYhq+PhVstkByk7GvAGGP7
        9mxaVDn9hpSFk5dIRc7xkiDtkxgDq2UTAe6ccJ4rD5MBRK08qaZrVUs5ArzWMkMu5SDcffvRffCJ
        S5klvz6sP6/Ez2KNj2u8267eqPuHW40jvY56Z9qGilph788I7+PpmRhjoHjV1loqyJJCyjhibTnu
        zlgYA27LuiLlmwlgvwGH2tIG0g2838ABTtIPwaX7t5EhloracXl06hg/9NZLXRqrn92Zk1AIJdw2
        s8RdOxE4r02n3ei0ClCfvBoYqyvjM69/kGoIr686Ohh2agCTI+a153IIz8JocoEty8lz0T5qv0fI
        cUv5UDrsE69zoUdAMJr572YucXdzC1X+D/0AIJLxlGfGUi7wdOAhzVLzx/0rrfe4bRjcznmqskKo
        kqyxol16KEw2S67zxTyeIUJwCP4AAAD//wMAqYUqgf0DAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8de4110529839e56-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Wed, 06 Nov 2024 09:30:43 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=loqAsRu86UykMKQMg3z8gB4BBDAuZ6Vca9ZfOTq66Qo-1730885443-*******-YK976xVsj2Yu_OpQs.IoHyxvN0bSXd._..jfqU8pn1HtpMMHXCDbEUpMcqNTuhYvuuA95HQGI6Jq_vjEVd_SLQ;
        path=/; expires=Wed, 06-Nov-24 10:00:43 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=LX36sP3b4IHGOqhbqRPeGY5sTMM8V.N5Kv_LqSGFIH8-1730885443866-*******-*********;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      openai-organization:
      - langchain
      openai-processing-ms:
      - '380'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999960'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_43e9f6e698f554ded303aa5ff5fe57b8
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "Context:
      The man is not steering a car\nQuestion: Can we logically conclude for sure
      that the man is not steering a jeep?\nOutput: "}], "model": "claude-3-haiku-20240307",
      "system": "Is the output accurate with respect to the context and question?
      Y/N", "tool_choice": {"type": "tool", "name": "accuracy"}, "tools": [{"name":
      "accuracy", "description": "Whether the output is accurate with respect to the
      context and question.", "input_schema": {"type": "object", "properties": {"score":
      {"type": "string", "enum": ["Y", "N"], "description": "The score for the evaluation,
      one of Y, N."}, "explanation": {"type": "string", "description": "The explanation
      for the score."}}, "required": ["score", "explanation"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA5ySTWsbMRCG/8owZ7msHZeke2ugpDQ5hKSUmiYYWRqvZcuarTRyshj/9yIt6Qcl
        l5yE5h0982pmjugstrhP3bKZ3m3p2xe6ve3s/G5qF93209U1PaFCGXoqWZSS7ggVRvYloFNySXQQ
        VLhnSx5bNF5nS5OzyUa7XZ7Mmtm8OWvOUaHhIBQE2x/HF6Iw+2VOBVl9lHteNtMPF8EuLs+v58Pq
        5v5Gf57e775fDagw6H0tbEyO2pSIC30WbI+YDMeiLVAhPfdeBy2OA7Z4qRNZ4ACyIejcgQJUL88C
        stECD1iEvQ7gEgQWSEIUXehAg9HxARU8ERgdiua5c0Z7PxSG8dkSrDlCypFG2uusLVH/Dj7Ws8ga
        ShuA13CgjTOeFKyy1Fe1sILEFffito98cJYsWKaRHrMn4DxW7Tklt3LeyfCvFcPZW1jR/2a+bijS
        miOpmsxZ+iwwtlfIDxBp7clIGoF/2vC2z+Pp9KgwCffLSDrV6fy1A1VI9DNTMIRtyN4rzHXn2uM4
        6aXwjkLC9v2sUTj6/R2bzi9Op18AAAD//wMAkleKc9MCAAA=
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8de41107cea917e8-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Wed, 06 Nov 2024 09:30:45 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      request-id:
      - req_01Viqb8TwVsSAoVK9VoijQww
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "Context:
      The man is not steering a SUV\nQuestion: Can we logically conclude for sure
      that the man is not steering a car?\nOutput: "}], "model": "claude-3-haiku-20240307",
      "system": "Is the output accurate with respect to the context and question?
      Y/N", "tool_choice": {"type": "tool", "name": "accuracy"}, "tools": [{"name":
      "accuracy", "description": "Whether the output is accurate with respect to the
      context and question.", "input_schema": {"type": "object", "properties": {"score":
      {"type": "string", "enum": ["Y", "N"], "description": "The score for the evaluation,
      one of Y, N."}, "explanation": {"type": "string", "description": "The explanation
      for the score."}}, "required": ["score", "explanation"]}}]}'
    headers: {}
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA4SSQW/bMAyF/wrBs1M4SbNhvg0tstMKDG1WbGthMDJjK5VFT5TSGkH++yAH3brD
        sJMgPerjo56OaBussNe2Ludr3ozD9ebL9f16f/Pp6kD3K75cYYFxHDhXsSq1jAUGcfmAVK1G8hEL
        7KVhhxUaR6nh2XLWkX1Ks0W5uCyX5Xss0IiP7CNWP46vxCji6qQZOfnI+1SX89ur73bRt5/3z/Hd
        t0B+vafOWyzQUz81NiYFMmO+54cUsTqiGglZu8EC+WVw5Cla8VjhXcfQ2gN7mCy8RNBIkRViRxEe
        MHYMPXmwCl6yyBysb4HgdvP1AQvYpgixswqN8LnGs8mPEawboWfy8G+GoXABH/OSRYJtEGo4gKHI
        rYTx7MJ641LDmltqASoT0fqdhH6aA4YgB9tw88cEOSfPkBSigJPWGnJuzDNOKNhJAE2Bzw3+Y/Cu
        48BAgUFixwFyQAqyy6rCltW+mvsbZyS5Brb8G3iBp9NjgRplqAOTTgm8yXkSlH8m9oax8sm5AtP0
        r6rjOc06yhN7xWq1KAuUFN+ezZcfTqdfAAAA//8DAGwLxC23AgAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8de411087a3ecfc8-SJC
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Wed, 06 Nov 2024 09:30:45 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      request-id:
      - req_01NPZFdoNbxrSN1nczYcmoZ8
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
version: 1
