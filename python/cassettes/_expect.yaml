interactions:
- request:
    body: '{"input": ["hello", "hi"], "model": "text-embedding-3-small", "encoding_format":
      "base64"}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, zstd
      connection:
      - keep-alive
      content-length:
      - '90'
      content-type:
      - application/json
      host:
      - api.openai.com
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.63.2
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.11.3
    method: POST
    uri: https://api.openai.com/v1/embeddings
  response:
    body:
      string: "{\n  \"object\": \"list\",\n  \"data\": [\n    {\n      \"object\":
        \"embedding\",\n      \"index\": 0,\n      \"embedding\": \"eb+JPPlBZL0Rtbk7XrSHPcCFEjy4wkG9FE3mvJh+ej311hC7cDczvZJOITz+bAi9H19IvCuN7LzmUG88gHluPbRlj70BvBk981omPFkyJz2uEIg9o/4lPFbxtruTXMI8a6exPFh7QjzS+py7hy6ePETFxzyj8IS9/mwIPQaVNr3O9KY8QvKgu2unMTxBzXK8fGXXvP8xDj1RGBo7/mwIvYtCtbwfCAy8QkldPfRoxzwmIhm4+Yp/PGriq7xfeY27AjgEPXfj9jxOaeA7lMqLvGUXMD3YQQM+AQU1PS+4kLwQ4hI92Ji/PB5RJzxBzfI7jpHGvMfoujtbvDK8hmkYPAQLKzxoZkG8Fjcau+KqITyvLEq8RTORPKtvb7rWHFU9zS8hPE4gxbz5iv87ZJtFvEfLPb0swLu8qMkhvIrU6ztjLXy99xcBO9JRWbyxXxm9kk4hveCyzDvfW5C9SlVJOwRi57vNL6G84MBtvERuCz1lFzC787FivAqbrDx6JGc7kxMnvARURrseQ4Y9pYixPIkPZj1COzy9FjeaPEvRM7wyB6I8n0FLPWx62Dwqf8s7AVzxvOZQ7zyZo6i97E46vUpj6rxu9sK8o/6lPO1qfD3xcHI99qm3vfCr7Dv7vU69+3Szu+iR37wu3H08V18AvT4npb3Nz/g7e+nsu47a4Tw6IS+9T9epOVLdnzfhLrc8bYj5u0VBsrpKVUm9kwWGPPoGar2hdJq7zMHXvF95jTvnvjg9GoarvC6TYjxetAe9JiIZvO6BCb0wfZa8fSpdPa1LAr3cYzu8V1+APDB9Fj2AeW69P0PnO0e9nLz1Lc28zApzPYjlAr2y24O8qUWMPM70Jj0cHlg8pXqQvPNMBb12xzS95YtpPGv+bTySlzw9sm26vKP+pTzRNZe87tjFPBH+VL34fN67Ztw1u8YjNTwx4vM8VFmKvAKBn71OEqS60rGBPOxOuj3/MQ47GRjivCK8+rx9igW8WvcsuOlIxLzEmak7fYoFPRDiEr2Dul49i0I1PbYqlTyNXvc7VSwxvXqSsD1hEbq8avBMu83mhTqzoIk8nsXgvLYqFT2LQjW8m3bPvFnSfrrEp0o7vMg3PWqZED3nzNm8Q1d+PV60h7xwKZK6i0I1PZ+K5rxho3C9V1+APQaVNjsFwg89OmpKvBW7L70BvBk9URiaPa4eqTzcY7s8XrSHvIMahzviqqG83dGEPFnSfrz6Buo82RQqPUC/UbwQ4hK9Xe+BPAQLKz2Kfa88rDT1vGUXsDmj8IQ7niWJvTvmtLoJjQs7gNmWPK8syjygr5Q77tjFO47aYb3Y73s7joMlO18ZZTzG2hk9DyuuPIX7Tr3ugYk89psWO6BP7LrwVLA8wIUSvKNVYr0IH8K8fSpdvHZ+mTuLmfG8HkMGvQBOUL1yaoI6uTALPMZ6cbwOWIc8MgciPNKxgbzL7jA9lvTuu7VzML2AIrI7LvOKPG6tp7vlNC09A+b8PEd0gb3LRW07DfjeO0VBMrsmeVW9NNrIPNJR2TxeC0S9xbXrPIuZ8Tz1Lc08ZFKqOxx+gDwDRqW8APeTvDmz5TzgIBY98AsVvFCcrzx7Vza9rfl6PRY3GjzjuMI7+q+tvM8CyLys3Tg9j1bMOn+mR7ytor4803aHvDB9Fr1+7+I8waFUPRnBpbq0riq9R3QBvd7txjzGenE7JOEoPHia27zU22Q8gxoHvTGLt7vgIBa8T9epvCvtFDrjYQY9a/5tu0UzEb3spfa8uYdHu7Lbg7190yA8ob01u0cUWT0Ys4Q7wg8eveg6ozz43Aa9dtXVPLCaE7xor1y96f+ovAXQsDvuIWG8qqrpu8Hq7zw9GYS84MDtu26fhjsPvWS6TskIPYwHO7wLblO84kr5u8PiRL2RKXM9YRG6vDJQvby4C109APcTvckbirw5pUQ8xtoZvfbyUj1LKHA9xzFWvQejV7wqNrA8qIAGPVUsMbtZMie8sJoTPee+uDyBPnS9NNrIPJJAADxJ/oy7EOISPc3mhTyJD2Y9L0rHPJq/aruNdQS9cI7vPG3agLy/wAw8e0mVvAgfwrwqKI+8maOoPRGnGLwtdyA9y+4wvToTDjwmwnC8XNj0PPXkMbzsl9W8MMaxvPHQmjxOaWC9YsiePJ8zqruM+Zm8SgyuvGxst7zBWLk642EGPT5+4bwJ5Mc87QUfvWVgSz3bkBQ9/Dm5u+NhhryEKCi8fK7yvEO3JjyVL2m8ZWDLPFgkBrx8Dhu8d4w6PfyQ9bySlzw9udBiPVquEb0gJE48mh+TvZw7Vb1vu8i7uvUQvNueNTxoD4W8RMVHOucV9TpfGeU89eSxuWhmwbsWgDW8V/9XvF3vgbysJlS9iQHFOdueNbyQDTG8gT50uqnlYzo0zKe81UmuuxG1ubxLiJg8EUdwOzfSHbtt6KE8LpNivGhYILuUygs8jRXcOskpqzvQfrK8/4hKPIQ2ST3kJow8JaYuvfXksbzNL6G85gfUvC+4EL1DV/48qMkhPWDeaj0zFUO8kk6hvEMAwrrcVRq4iaqIu/u9zrp/Xay8EPAzuwLKOjsjHKO8xmxQPTQjZLzmsJc9Ut0fPGteljuJAUW9aK/cutTbZDzPAkg9Z+pWPeawl7zuyiS9mVqNvEaYbrqv1Y26hy4evRtZUr3kfci8llSXvEKSeLzXKnY7+8tvPZ4libyDY6K8NejpunvpbDzPuay8Ng0YPHH8uDzNeLy8428nvQEFNb2XYji8GLMEvD+jjzzTFl+82ssOveiR3zyuEAg9b2SMu2VubLwG3tE7cUXUO2CVz7zjYYa9vxfJumUXsDz9p4I8iErgvHnNKrwXjta6DliHOlnpCzzylaA8W7wyvOTGYzznFfW88FSwPCWmrjwEC6u7d0MfvFCcr7xNpNo8OhMOPbgZfrsSIwM9vNbYPJdw2bor+7W8mfpkPJTYLLyZo6g7Dx0NvGONJLx/Xay8he0tPAbe0TyS4Ne8BAsrPBl4Cj1WOlI6QAhtu07JCD0hoLg7eiRnvLIkH72mPxa98Rk2vI7aYToPKy694pwAvaphzjyfQcu88SdXu64eKT1fh648c4ZEO5XmzbyX0AG95YvpvNhPpLxiaPa8ojkgPJlajTuZ+uQ8qICGPMN0+zxeC0Q8HkMGuwdavDx6JOc7X3kNPAgfQryLi1A7BuzyvOiDPrwWyVC7bp8GPJ8zKrxzhsQ8Q1d+vXFT9Txseti8Rk/TO/96qbp1EFC7CY0LvdL6nLzzseI8waHUOwKBH72w8c+8fYoFPeb5MjxrtVI8xmxQO5Bk7bv4M0O9SNnevPXksTzL/FE9n0FLPd3RhDxYbSG8HdW8u20xPb2OOgo8lvTuPPjFeTzylaA8leZNvQT9Cb0vuJC8crOdPIQoqLvx0Jo8pYgxvdbFmDyUamO7bTE9PCGgOLyKxso8/bWjPHfj9juebqQ8W7wyvDohr7sKqU089GjHvLH/8LrVAJO8niWJPCfnHjyGwFS97+bmu9Ga9Dq+UsO7gT50PPzwHTz6Bmq8BP2JvMyzNjqI5YI8/acCPFe2PDtYJIY7qeXju02ye7yGaZg8+MV5PSKu2byWq1M9y+4wO2RSqju+CSi9S4gYvDO+Br1DqYW8EaeYPIjlAr2eJYk6aXTiu9N2Bz2sNHU9BFTGO5/qjrwwD808JnlVvKKQ3LvLRW07qMmhu++PqjsltE+8EbU5PY6RRjqCrL08A0YlvejxhzxvZAy9s+mkvG5N/zvDiwg84CAWOwFccbx/Xaw8b8lpPG6fBj2w8c+8MxXDPCuN7LxHvZy8QXa2PBDwMz0tztw7b7tIvSKuWTy+UsO8PJ0ZvcUVlLxvZAy90xZfvLhrhTizMkC9m3ZPvIjlgrv/0eU8V/9XPDIHIjp6hA+8s0BhPEI7vDzribQ82Ji/uBG1Ob2VL+m8revZvJ58Rb0hkhc8SZBDPFEYGjz7dLO5a16WO3bHtLsd1Tw8in2vPBbXcbwu8wo8EOISvD41RjwvASy7Y9a/PB5DBrzkfUi9xiO1PFzYdDwDj8A8bYj5POKcAD2FpBI93dGEvLx/nDzsl1W7lMoLvNhPJL30EQu88AuVvCX96jsx1NI8bk3/vPapt7tAv1E7fpimPIPIf7zh5Ru8cfy4uPzwHb311hA9cy+IPFImuzzO9KY8JzC6u+4h4bswfRa9XI9ZvBnBpTxuTX88AQW1vGJ/A7w44L68DlgHu0HN8rxBLZs83pYKPW2I+TturSe94qqhOpfQgbqfQcu8XUY+PIQoKD0C2Nu7A4/Auf2nAr3inAC9F5x3O42+H7xkUiq8BdAwO9oiyzw89NW8iPOjPNWgart5Fka9IxwjPC8BrLyfimY8vyVqPDlOCD0WN5o8ryxKu3Ye8bzm+bI6+Yr/PFUeEDxuTX87hsDUuqmOJ72uHik8+mYSvbdG17yY3iK8VL7nO2Jo9jwcx5u6qICGvNTb5LzL7rC80H6yOojlAr1xU/U8izQUOgx89Dsk78k8PPTVuwx89Lzb59C8d+P2O29kjLyHznW8L0pHPcv8Ubz6Buq5DlgHPOiRX72sNPU79dYQPFeom7zsQBm9hDZJPXrbyzw72JM61VfPPFDz67wUrQ49o/6lvLY4tjsPHQ09WG0hPFpO6bnLpZW8GoYrvXHuFz1620s9P+wqvV60hzxKVcm8+yuYvGnUCj34M0O85lBvvLdUeLwcHlg8yRuKub02ATtNsvs7NMynvJn65DyQDbE5GLOEOv+ISjzh5Ru9cgpaPOuJNDxsIxy9kMSVPKcEnLqU2Ky7o1XivElHKLwd4128C27Tu8o3zDqs3bg89BELPOMBXryOOoo8DfhevJw7Vbw/7Cq9+q8tPKOefTyYNd872tmvPHkWxry++4Y8Q6kFPWrwzDxq8Ey8IelTvFOUBLzeNmI8Kn/LOxDwM7yV5s28pk03PSbC8LqVL2k7itTrvLu6Fj30Hyy84XfSPHCATj34JSK69BGLOwqbrDzvRo+80Zp0OkR8LDvfW5A8qIAGPeQmjDx5zSo8gNkWPUCxsLoZeAo8LvOKPGPk4LwzXl47QYTXPMvusDxNpFo8zpR+vN7fJb1sw/M8F0W7vD41RjwKqc089eQxvJqxSbxys528oAbRvK35+jo02kg8PjVGPBtLsbzDi4g8kk4hPdvn0LyTBYa8+MV5PJofk7sE/Qk9PXDAvGFa1Tv2m5Y8OhMOPamOp7z7y+88kYmbPP8xDj1bvDK8/gxgu3UCrzxarpE8W3MXPRnBpbwPdMk8SZ5kPJMFhrz11pC8y6UVvKAGUTyv4y69Ig4CvXkWxjzPEGk82suOPH7vYjxetAe9LRf4PA8dDTseQwY89xeBO93RBD3SsYE8YRE6POEuN7qzoAk9mexDvOXrkTscEDc87sqkPHVZazwFJ2090YzTPGkdprwiDoI8BwOAPJlaDbz9/j48uBl+u6QMRzv5oQy9MA/NPBl4ijxjRIk82RQqPY46irovASw9ICROvKS1Cj14+oO8OaXEPAzcHLwx1FK9OO7fu1Ldn7svSke8bCOcvAqpzby375q8WdL+vN3RBD2kGmg8ODf7OxJsHj2DYyK8M74GO8rgD7xFQbK7O9gTO1oFzjy2j/I89BELunIKWjxoWKC85euRPCljCb1/tGg87sqkvJ7F4LzndR23uGsFvV95jbumP5Y83t+lu1Imuzt3Qx88U6Ilu4NjojubLbQ8N3L1O1tzl7yFpBI8eV9hvMm74bgWNxo8X3kNPEb4ljtYJAY9OVypvMPixLwuk2I8f12sO+4h4bztXNs8Yn+DPLLbA7yE34w8BcKPvBUS7LsNoSK93GM7PHHulzz0aEc9/mwIPed1nbriqqG8mCc+PCoojzwiZb46Gc/Gu23oobwwHW67qIAGvHOGRLuslJ08b8npPLsDMrzwq2y8hCiovK2ivjyav2q8sPFPvVh7wrxuTf87vvsGveZQbzuLQrW8Kn/LPMwhADxRb1a7uvWQPIjzIz0Glba8iaoIvagg3jxd/aI8vvsGvErDErstLgW95rCXuojlAj3Ep0q8dse0vOawl7yxXxk9AbwZPeQmDL3KgGc8Xe+BvHKznbxZ6Ys8EiMDvYjlAj4alMy8l2I4vU2k2jtYe0I81yr2u3HuF7qDut46HkMGOzfSHbzo8Qe8CS1jvGl04rx1WWu6cUXUuxUEyzxP16k8rUsCvKAG0bt4qHy8ooK7ujXoabzMwdc74S43Ox5Rpzuav+q8bCOcvCW0z7tSNNw8SIIiuzpqyjvVV888DlgHPVdfALztXFs8wervu3KznbxVLLG8MdTSOy+4kLvkfUg8g7reu++PqjzJu2G8CBGhPJt2z7vFtes7Wk5pOxizhLze7UY7GwIWvKWIMT044D48xPDlu0MAwjyoySG7Emweu51gAz21ymw9T46Oui+4kDrlQs475lDvPNVXzzwd4108+NwGPNSSSbyefEU9oK+UvHLBvruoyaE8MlA9vPw5uTraIss6o0dBO5ofEzzjbye814qePARUxrzre5M7GXiKulcN+TtW8TY9H21pPHCAzjvSUVk8+q+tOwOPQDvJKSu89S1NukvRMzyNzMA7pYgxPCWmrrxbylM85YvpPK8sSr3vncs8RTMRO5/qjjvJcsa82ssOuzZWsztT+WE8zGqbvHdDn7w0gww9rDR1u9rLDr3RjNO8kuDXu7i0oLyX0IG8HYyhPNxVmjuwmhO9qZzIO8bambtkqeY7ZpOaOxqUzDxmM/I8X4cuu/jcBr1Uvuc853WdvPnqpzzRjNM8/mwIPWVgy7wE/Qm80vocvErDEjzoOqM7SNleun0cvDwIH0I8aFggvZcZHTwcHlg7qgqSvBCC6ryI5YK8+CUiPK1LgjyTXEI8+71OPHiaWzyrz5e8DDPZvGteFr2wmpO8wmZaPN+kK7zNz3g8UPPrO/vL77wrRFE8zXi8PO28AzxNpFq73/vnvPFw8rxg3uq7WTKnO55upDwahis8siQfPHiofLza2a+7tPdFPYuL0LxiaPY7OaXEO+NvJz2Y3qI7nnxFvNL6nDyK1Gs8Buzyu523P71urSe9OhMOPFBTlLu1yuy80MfNPC7c/ToiDoK71NtkvIZpmDpWOlK8cCmSuuKqIT1tMb27rVmjvJ4libxDV/68ZiVRvcCTszzDi4i8XsKovA90ybunW1g7im8OPTfSHT3u2MW8EUdwuyIOgjsJjYs7qMmhu635+jv2mxY897fYvGYz8rp9HDw9mezDPBeOVjxj1j89o/6lPO1q/LqqChK6w4sIvT7eCTzoOqO87KX2uf7DxDxp1Iq8WvesvALKOryslJ08I3NfPSDbsrpho3A79eSxPNbFmLtUWYo8pwQcvUd0gTzQfjK7+vhIuwaHlbtyaoK8FslQvOJKebwATlC7W8rTPAqpTT23/bu7mh8TPBDwMzxiaPa7iaoIPf8xjrzxcPK8EDnPuwu37rwX/B+7Wq4RvAcDAD3ZFKo7w3R7O+jxBzwkmI08VR6QvCm6RTwbS7G8TsmIu61LAj1GBrg7cDezOuxAGbwEYme7MH0WvZgnPjwqKA8870YPPXMviDuhFPK7bYj5O4s0FLu7Wm66uBl+PIJVAbxvyWm7ibipu+1q/LkorCQ9HB7YPHqSMDtPLma81yr2PKaWUjzWxRi7Irx6Ox5Dhjx4+oM8Pt6JOwpSEbwHTJs4N3L1upDEFTtbvLK8FE3mPNrZr7thETo7/PAdPSvtlLxx/Lg8n+oOPHugUblJR6i8Jz7bO5MTpzpzL4i8JnlVvE2kWjwyByK8vvsGPUTFxztYxF09IlcdPZEpczzdKEG7dtVVvJe59LwmeVU7F/yfu1qukbwy+YA9083Du6gg3jt4qHy7YN5qPMFKGLt+Tws9EOISPOuJNDsQ4hK9GcElPQPm/Lw5Toi7YJVPvHm/ibuebiQ9o/AEvclyRjufQcs8crMdvGNEiTyLmXE9c3ijvE+ODjyl3+27/JD1vFdfAL0TMaQ7yuCPPDvYE72jnn088Rm2vN1xXLteC0S8bMPzvDHUUjztE0C8AVxxPaQMx7xwKRI8j/+Pu3CO7zwVBMs7Q6kFvN/757tNsnu9PKu6O/cXAbzWHNU8wJMzPDO+hrzwVLA6tiqVOONhBr3G2pk7081DPHUQUDx4mts79qk3vIjlgrzvRo+8\"\n
        \   },\n    {\n      \"object\": \"embedding\",\n      \"index\": 1,\n      \"embedding\":
        \"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\"\n
        \   }\n  ],\n  \"model\": \"text-embedding-3-small\",\n  \"usage\": {\n    \"prompt_tokens\":
        2,\n    \"total_tokens\": 2\n  }\n}\n"
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 91b554e7bbf67cb5-LAX
      Connection:
      - keep-alive
      Content-Type:
      - application/json
      Date:
      - Wed, 05 Mar 2025 00:00:11 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=daezjbKmFemZ5d12Mgbv63DSXyn_DqAeom.hvWSyYuk-1741132811-*******-jawnkEhxWsrcOZqheT4obA2ogcXi5yMX_gyH3rA8c2VG20msmocFcc8Ztouv_viBGKMS..kZDoIq8PiPPl1dbQH10mAk0e4xetTDlU15pCY;
        path=/; expires=Wed, 05-Mar-25 00:30:11 GMT; domain=.api.openai.com; HttpOnly;
        Secure; SameSite=None
      - _cfuvid=3GLWueocQzXmOenEN1QU4nkdH4zOgE5l9EhPpgscIcc-1741132811866-*******-604800000;
        path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      access-control-allow-origin:
      - '*'
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      content-length:
      - '16688'
      openai-model:
      - text-embedding-3-small
      openai-organization:
      - langchain
      openai-processing-ms:
      - '48'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      via:
      - envoy-router-74bc558f48-lf77n
      x-envoy-upstream-service-time:
      - '27'
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '10000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '9999998'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_46af97e63e5b6ca9dd1868f7d7abcbef
    status:
      code: 200
      message: OK
version: 1
